import * as process from 'process';
import axios from 'axios';
import logger from '../config/logger';
import _ from 'lodash';
import IRequest from 'IRequest';

interface RequestBodyGetSuggestList {
  body: {
    phone: string;
    birth: string;
    citizenId: string;
    fosId: string;
  }
}

class SuggestAccNoService {
  public getSuggestAccountList = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        if (!req
            || !req.body
            || !req.body.hasOwnProperty('phone')
            || !req.body.hasOwnProperty('birth')
            || !req.body.hasOwnProperty('citizenId')
            || !req.body.hasOwnProperty('fosId')) {
          return resolvePr({
            data: [],
            messageErr: 'Call error param not valid',
            status: 'FAILED',
          });
        }

        const { phone, birth, citizenId, fosId } = req.body;

        const data = JSON.stringify({
          phone,
          birth,
          citizenId,
          fosId,
        });

        const configCall = {
          data,
          method: 'post',
          maxBodyLength: Infinity,
          url: `${process.env.SUGGEST_ACCOUNT_LIST_URL}/suggest-account-number`,
          headers: {
            'Content-Type': 'application/json',
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              logger.info(JSON.stringify(response.data));

              // tslint:disable-next-line:prefer-const variable-name
              const { status, message: message_1, data } = response.data;

              return resolvePr({ status, data, message: message_1 });
            })
            .catch((error) => {
              console.log('error: ', error);
              if(error.response){
                const { data, status } = error.response
                return resolvePr({ data: data.data, message: data?.message, codeALT: status, status: data.status });
              } else if(error.request){
                logger.info('Lỗi: Vui lòng kiểm tra lại kết nối: ' + configCall.url);
                return resolvePr({ data: [], messageErr: error.code  });
              }
              return resolvePr({ data: [], messageErr: error});
            });
      });
      // tslint:disable-next-line:variable-name
    } catch (error_1) {
      console.log('error_1',error_1);
      return { codeALT: '209', messageErr: JSON.stringify(error_1) };
    }
  }

  public checkTempExist = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr, rejectPr) => {
        if (!req
            || !req.body
            || !req.body.hasOwnProperty('accNum')
            || !req.body.hasOwnProperty('fosId')) {
          return resolvePr({
            data: {},
            messageErr: 'Call error param not valid',
            status: 'FAILED',
          });
        }

        const { accNum, fosId } = req.body;

        const data = JSON.stringify({
          accNum,
          fosId,
        });

        const configCall = {
          data,
          method: 'post',
          maxBodyLength: Infinity,
          url: `${process.env.SUGGEST_ACCOUNT_LIST_URL}/check-temp-exist`,
          headers: {
            'Content-Type': 'application/json',
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              logger.info(JSON.stringify(response.data));

              // tslint:disable-next-line:prefer-const variable-name
              const { status, message: message_1, data } = response.data;

              return resolvePr({ status, data, message: message_1 });
            })
            .catch((error) => {
              // tslint:disable-next-line:max-line-length
              if(error.response){
                const { data, status } = error.response
                return resolvePr({ data: data.data, message: data?.message, codeALT: status, status: data.status });
              } else if(error.request){
                logger.info('Lỗi: Vui lòng kiểm tra lại kết nối: ' + configCall.url);
                return resolvePr({ data: [], messageErr: error.code  });
              }
              return resolvePr({ data: [], messageErr: JSON.stringify(error) });
            });
      });
      // tslint:disable-next-line:variable-name
    } catch (error_1) {
      logger.info(error_1);
      return { codeALT: '209', message: JSON.stringify(error_1) };
    }
  }
}
const insSuggestAccNoService = new SuggestAccNoService();

export default insSuggestAccNoService
