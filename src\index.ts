import 'reflect-metadata';
import path from 'path';
if (process.env.ENV === 'dev') {
 require('dotenv').config({
        path: path.join(process.cwd(), './env/dev/.env'),
    });
} else if (process.env.ENV === 'pro') {
    require('dotenv').config({
        path: path.join(process.cwd(), './env/pro/.env'),
    });
}

import logger from './config/logger';
import app from './config/express';
import glb_sv from './share/global_service';
import { terminate } from './terminate';
import databaseConnectionService from './database/DatabaseConnectionService';

const PORT = Number(process.env.PORT) || 8500;

glb_sv.LIST_SEC = process.env.LIST_SEC.split(',');

// Initialize application
const initializeApp = async () => {
    try {
        // Connect to MongoDB
        databaseConnectionService.connect().then(() => {
            logger.info('✅ Database connected successfully');
        }).catch( (error) => {
            logger.error('❌ Failed to connect to MongoDB:', error);
            // process.exit(1);
        });
        // Start server
        const server = app.listen(PORT, () => {
            logger.info(`🚀 Server running at port ${PORT}`);
            logger.info(`🌍 Environment: ${process.env.ENV}`);
        });

        return server;
    } catch (error) {
        logger.error('❌ Failed to initialize application:', error);
        process.exit(1);
    }
};

// Start the application
initializeApp().then((server) => {
    if (!server) return;

    //-- Start Handle nodejs process errors
    process.on('beforeExit', async (code) => {
        // Can make asynchronous calls
        logger.info(`Process will exit with code: ${code}`);
        setInterval(() => {
            logger.info('FOSEkyc is running...');
        }, 10000);

        // Disconnect from database
        await databaseConnectionService.disconnect();

        setTimeout(() => {
            process.exit(code);
        }, 100);
    });

    process.on('exit', (code) => {
        // Only synchronous calls
        logger.error(`Process exited with code: ${code}`);
    });

    const exitHandler = terminate(server, {
        coredump: false,
        timeout: 500,
    });

    process.on('uncaughtException', exitHandler(1, 'Unexpected Error'));
    process.on('unhandledRejection', exitHandler(1, 'Unhandled Promise'));
    process.on(
        'uncaughtExceptionMonitor',
        exitHandler(1, 'unhandled rejection'),
    );
    // -----
    process.on('SIGTERM', exitHandler(0, 'SIGTERM'));
    //catches ctrl+c event
    process.on('SIGINT', exitHandler(0, 'SIGINT'));
    // catches "kill pid" (for example: nodemon restart)
    process.on('SIGUSR1', exitHandler(0, 'SIGUSR1'));
    process.on('SIGUSR2', exitHandler(0, 'SIGUSR2'));
    //-- End Handle nodejs process errors

    setInterval(() => {
        logger.info('FOSEkyc is running...');
    }, 10000);

    glb_sv.getLastestDataReport();
}).catch((error) => {
    logger.error('❌ Application startup failed:', error);
    process.exit(1);
});
