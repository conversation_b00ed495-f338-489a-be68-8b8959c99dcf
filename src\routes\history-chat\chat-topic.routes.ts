import { Router } from 'express';
import {
    // ChatCusTopic controllers
    createChatTopic,
    updateChatTopic,
    deleteChatTopic,
    getChatTopics,
    getChatTopicById,
    getChatTopicsByFosId,

    // ChatCusTopicMsg controllers
    createMessage,
    updateMessage,
    deleteMessage,
    getMessagesByTopicId,
    updateMessageReaction
} from '../../controllers/chat-topic.controller';

import {
    validateChatTopic,
    validateUpdateChatTopic,
    validateMessage,
    validateUpdateMessage,
    validateReactionUpdate,
    validatePagination,
    validatePathParams
} from '../../middleware/chat-topic.validation';

const router = Router();

// ============ ChatCusTopic Routes ============

/**
 * @route POST /api/chat-topics
 * @desc Tạo chat topic mới
 * @body {ChatCusTopic} - Dữ liệu chat topic
 */
router.post('/chat-topics', validateChatTopic, createChatTopic);

/**
 * @route PUT /api/chat-topics/:fos_id/:topic_id
 * @desc Cập nhật chat topic
 * @params {string} fos_id - FOS ID
 * @params {string} topic_id - Topic ID
 * @body {Partial<ChatCusTopic>} - Dữ liệu cập nhật
 */
router.put('/chat-topics/:fos_id/:topic_id', validateUpdateChatTopic, updateChatTopic);

/**
 * @route DELETE /api/chat-topics/:fos_id/:topic_id
 * @desc Xóa chat topic
 * @params {string} fos_id - FOS ID
 * @params {string} topic_id - Topic ID
 */
router.delete('/chat-topics/:fos_id/:topic_id', validatePathParams, deleteChatTopic);

/**
 * @route GET /api/chat-topics
 * @desc Lấy danh sách chat topics với filter và phân trang
 * @query {number} page - Số trang (default: 1)
 * @query {number} limit - Số lượng items per page (default: 10)
 * @query {object} filter - Các điều kiện filter khác
 */
router.get('/chat-topics', validatePagination, getChatTopics);

/**
 * @route GET /api/chat-topics/:fos_id/:topic_id
 * @desc Lấy chat topic theo ID
 * @params {string} fos_id - FOS ID
 * @params {string} topic_id - Topic ID
 */
router.get('/chat-topics/:fos_id/:topic_id', validatePathParams, getChatTopicById);

/**
 * @route GET /api/chat-topics/fos/:fos_id
 * @desc Lấy tất cả chat topics theo fos_id
 * @params {string} fos_id - FOS ID
 * @query {number} page - Số trang (default: 1)
 * @query {number} limit - Số lượng items per page (default: 10)
 */
router.get('/chat-topics/fos/:fos_id', validatePathParams, validatePagination, getChatTopicsByFosId);

// ============ ChatCusTopicMsg Routes ============

/**
 * @route POST /api/messages
 * @desc Tạo message mới
 * @body {ChatCusTopicMsg} - Dữ liệu message
 */
router.post('/messages', validateMessage, createMessage);

/**
 * @route PUT /api/messages/:messageId
 * @desc Cập nhật message
 * @params {string} messageId - Message ID
 * @body {Partial<ChatCusTopicMsg>} - Dữ liệu cập nhật
 */
router.put('/messages/:messageId', validateUpdateMessage, updateMessage);

/**
 * @route DELETE /api/messages/:messageId
 * @desc Xóa message
 * @params {string} messageId - Message ID
 */
router.delete('/messages/:messageId', validatePathParams, deleteMessage);

/**
 * @route GET /api/messages/topic/:topicId
 * @desc Lấy tất cả messages theo topic_id
 * @params {string} topicId - Topic ID
 * @query {number} page - Số trang (default: 1)
 * @query {number} limit - Số lượng items per page (default: 10)
 */
router.get('/messages/topic/:topicId', validatePathParams, validatePagination, getMessagesByTopicId);

/**
 * @route put /api/messages/:messageId/reaction
 * @desc Cập nhật reaction cho message
 * @params {string} messageId - Message ID
 * @body {number} reaction - Reaction value
 */
router.put('/messages/:messageId/reaction', validateReactionUpdate, updateMessageReaction);

export default router;
