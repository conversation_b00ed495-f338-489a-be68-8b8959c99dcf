const request = require('request')
const fs = require('fs')
var FormData = require('form-data');
var path = require('path')
import glb_sv from '../share/global_service'
import logger from '../config/logger';
import axios from 'axios'
import moment from 'moment';

// Nhacvb 6661
const TIME_RETRY =  process.env.TIME_RETRY || 1000
const COUNT_RETRY =  process.env.TIME_RETRY || 3

class APIFptBase {

    public makeOptionIdRecognition = (image: any) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_ID_RECOGNITION,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            formData: {
                "image": image,
                "face": "1"     // cho phép cắt hình từ image
            }
        }
        return option
    }

    public makeOptionLiveness = (image: any, video: any) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_LIVENESS,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            formData: {
                "cmnd": image,
                "video": video,
                'detect_mask': 'true',
                'ignore_deepfake': 'true',
                'face_validation': '1',
                'return_base64': '1', // return data in video_photo_base64
            }
        }
        return option
    }

    public makeOptionFaceMatch = (image: any, imageCompare: any) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_FACE_MATCH,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            // formData: {
            //     "file[0]": image,
            //     "file[1]": imageCompare,
            // }
            formData: {
                'file[1]': { 
                  'value': image,
                  'options': {
                    'filename': 'z3626880065368_2f8b61ab3d3c880e49c096ff807e940f.jpg',
                    // @ts-ignore
                    'contentType': null
                  }
                },
                'file[2]': {
                  'value': imageCompare,
                  'options': {
                    'filename': 'IMG20210531081220.jpeg',
                    // @ts-ignore
                    'contentType': null
                  }
                }
              }
        }

        return option
    }

    // Create new user for Collection
    public makeCreateUserInFPTFaceSearchOption = (userID: string, userName: string, collectionName: string) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_CREATE_COLLECTION,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            formData: {
                "collection": collectionName,
                "id": userID,
                'name': userName
            }
        }
        logger.info(`makeCreateUserInFPTFaceSearchOption: ${JSON.stringify(option)}`, );
        return option
    }

    // Add image into Collection
    public makeAddFaceToFPTFaceSearchCollectionOption = (userID: string, collectionName: string, image: any) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_ADD_FACE,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            formData: {
                "collection": collectionName,
                "id": userID,
                'file': image
            }
        }
        logger.info(`makeAddFaceToFPTFaceSearchCollectionOption: ${JSON.stringify(option)}`,);
        return option
    }

    // Search face in Collection
    public makeFaceSearchOption = (threshold: number, collectionName: string, image: any) => {
        const option = {
            method: "POST",
            url: process.env.FPT_DOMAIN_SEARCH_FACE,
            port: 443,
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY
            },
            formData: {
                "collection": collectionName,
                "threshold": threshold,
                'file': image
            }
        }
        logger.info(`makeFaceSearchOption: ${JSON.stringify(option)}`,);
        return option
    }
}

class FptService extends APIFptBase {
    
    call = (infoReq: any, pathFile: string): any => {
        return new Promise((resolve, reject) => {
            switch (infoReq.step) {
                case '1':
                case '2':
                    return this.callIdRecognizie(pathFile)
                        .then((data) => {
                            return resolve(data)
                        })
                        .catch(err => {
                            logger.error('callIdRecognizie', { error: err, pathFile })
                            return reject(err)
                        })
                case '3':
                    if (infoReq.isImageFaceMatch) {
                        return this.faceMatch(infoReq, pathFile)
                            .then((data) => {
                                return resolve(data)
                            })
                            .catch(err => {
                                logger.error('faceMatch', { error: err, infoReq, pathFile })
                                return reject(err)
                            })
                    } else {
                        return this.callLiveness(infoReq, pathFile)
                            .then((data) => {
                                return resolve(data)
                            })
                            .catch(err => {
                                logger.error('callLiveness', { error: err, infoReq, pathFile })
                                return reject(err)
                            })
                    }
                default:
                    return reject({ codeALT: '202', messageErr: 'API incorrect syntax' })
            }
        })
    }
    
    // đã retry
    private callIdRecognizie = (pathImage: string): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {                   
                const result: any = await this.callRetryRecognizie(Number(COUNT_RETRY), pathImage, null)
                if(result.status != 200){ 
                    logger.error('============== ERROR API FPT callIdRecognizie ================ status: ' + result.status)
                    return reject({ codeALT: result.status,  errorMessage: 'API FPT: ' + result.data.message  })
                }
                let data = result.data
                logger.info('==============CHECK callIdRecognizie ================')
                if(data &&  Array.isArray(data.data) && data.data.length > 0 &&  data.data[0].type !='old' ){
                    logger.info('==============START CHECK================')
                    const doe = data.data[0].doe ? data.data[0].doe :"31/12/2099" ;
                    let checkStrValid = /^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/.test(doe);
                    let check =  moment( doe,'DD/MM/YYYY').isValid() ;
                    logger.info(`callIdRecognizie  ${JSON.stringify({
                        doe:  data.data[0].doe,
                        checkStrValid,
                        check
                    })}`)
                    if(!checkStrValid || !check){
                        let _data = data.data.map((item1 :any ,index :number )=> {
                            return index === 0 ? {...item1, doe: "31/12/2099"} : item1;
                        })
                        return resolve({
                            errorMessage: data.errorMessage,
                            errorCode: data.errorCode,
                            data: _data
                        })              
                    }
                }
                return resolve(data)
            } catch (error) {
                logger.error('============== ERROR API FPT callIdRecognizie ================')
                logger.error(error)
                logger.error('============== END ERROR API FPT callIdRecognizie ================\n')
                return reject({ codeALT: '210', messageErr: 'API FPT error' })
            }
        })
    }

    // Retry-Recognizie
    private callRetryRecognizie = async (count: number, pathImage: any, returnOld: any):Promise<any> => {
        if(count <= 0){
            return returnOld 
        }
        try {
            const FormData = require('form-data');
            let option = this.makeOptionIdRecognition(fs.createReadStream(pathImage))
            const form = new FormData();
            form.append('image',    option.formData.image)
            form.append('face',     option.formData.face)
            const config : any= {
                method: option.method,
                url:    option.url,
                headers: {...option.headers,...form.getHeaders()},
                data: form
            }
            const result = await axios.request(config)
            if([200, 201].includes(result.status)){
                return result
            }else {
                await this.sleep(Number(TIME_RETRY))
                return await this.callRetryRecognizie(count-1, pathImage, result) 
            }
        } catch (error) {
            // console.log('error: ', error);
            logger.error('============== ERROR REQ callRetryRecognizie ================')
            if(error.response){
                logger.error(JSON.stringify(error.response.data));
            }
            await this.sleep(Number(TIME_RETRY))
            return await this.callRetryRecognizie(count-1, pathImage, error.response) 
        }
    }
    
    private sleep = async(_time:number)=>{
        const time = _time || 0
        await new Promise(resolve => setTimeout(resolve, time));
    }

    private callLiveness = (infoReq: any, pathVideo: string): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {
                const pathUser = glb_sv.getPathUser(infoReq.sec, infoReq.userId)
                const pathFrontImage = path.join(pathUser, '/front_id')
                if (!fs.existsSync(pathFrontImage)) {
                    reject({ codeALT: '103', messageErr: "Front image is invalid" })
                    return
                }
                let filenames = fs.readdirSync(pathFrontImage)
                    
                if (filenames.length == 0) {
                    return reject({ codeALT: '103', messageErr: "Front image is invalid" })
                }

                let pathImage   = path.join(pathFrontImage, filenames[0])
                try {                   
                    const result: any = await this.callRetryLiveness(Number(COUNT_RETRY),pathImage, pathVideo, null)
                    let data = result.data
                    if([200, 201].includes(result.status)){
                        logger.info(`${moment().format('DD/MM/YYYY HH:mm:ss')} `, `[callLiveness]: ${JSON.stringify(data)}`)
                        return resolve( data)
                    }else{                    
                        logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} ` , '============== ERROR API FPT callLiveness ================ status: ' + result.status)
                        return reject({ codeALT: result.status,  errorMessage: 'API FPT: ' + result.data.message  })
                    }
                } catch (error) {
                    logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `+ 'callLiveness err: ')
                    reject({ codeALT: '210', messageErr: 'API FPT error' })
                }
            } catch (error) {
                return reject({ codeALT: '209', messageErr: error })
            }
        })
    }

    // Retry-Liveness
    private callRetryLiveness = async (count: number, pathImage: string,pathVideo: string, returnOld: any):Promise<any> => {
        if(count <= 0){
            return returnOld 
        }
        try {
            const FormData = require('form-data');
            let option = this.makeOptionLiveness(fs.createReadStream(pathImage), fs.createReadStream(pathVideo))
            const form = new FormData();
            form.append('cmnd', option.formData.cmnd)
            form.append('video', option.formData.video)
            form.append('detect_mask', option.formData.detect_mask)
            form.append('ignore_deepfake', option.formData.ignore_deepfake)
            form.append('face_validation', option.formData.face_validation)
            form.append('return_base64', option.formData.return_base64)
            const config : any= {
                method: option.method,
                url:    option.url,
                headers: {...option.headers,...form.getHeaders()},
                data: form
            }
            const result = await axios.request(config)
            if([200, 201].includes(result.status)){
                return result
            }else {
                await this.sleep(Number(TIME_RETRY))
                logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `, 'callLiveness err retry: '+ count)
                return await this.callRetryLiveness(count-1, pathImage, pathVideo, result) 
            }
        } catch (error) {
            logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `, 'callLiveness err retry: '+ count +" | "+ JSON.stringify(error.response.data ))
            await this.sleep(Number(TIME_RETRY))
            return await this.callRetryLiveness(count-1, pathImage, pathVideo, error.response) 
        }
    }

    public createUserInFPTFaceSearch = (userID: string, userName: string, collectionName: string): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {
                const reqData ={
                    userID, userName, collectionName
                }
                const result: any = await this.retryCreateUserInFPTFaceSearch(Number(COUNT_RETRY),reqData, null)
                logger.info(`[${process.env.FPT_FACE_SEARCH_DOMAIN}/create]: ${JSON.stringify(result.data)}`)
                console.log(`${moment().format('DD/MM/YYYY HH:mm:ss')} `,"createCollection res statusCode: ", result.status)
                if(result.status == 200){
                    let data = result.data
                    resolve(data)
                }else{
                    reject({ codeALT: '210', messageErr: 'API FPT error: '+ result.data.message})
                }
            } catch (error) {
                console.log('error: ', error);
                console.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `,'createUserInFPTFaceSearch err', error)
                return reject({ codeALT: '201', messageErr: error })
            }
        })
    }

    // Retry-CreateUserInFPTFaceSearch
    private retryCreateUserInFPTFaceSearch = async (count: number, reqData: any, returnOld?: any):Promise<any>=>{
        console.log('retryCreateUserInFPTFaceSearch count: ', Number(COUNT_RETRY) - count);
        if(count <= 0){
            return returnOld 
        }
        try {
            const {userID, userName, collectionName} = reqData
            let option = this.makeCreateUserInFPTFaceSearchOption(userID, userName, collectionName)
            const form = new FormData();
            form.append('collection', option.formData.collection)
            form.append('id', option.formData.id)
            form.append('name', option.formData.name)
            const config : any= {
                method: option.method,
                url: option.url,
                headers: {...option.headers,...form.getHeaders()},
                data: form
            }
            const result: any = await axios.request(config)
            if([200, 201].includes(result.status)){
                return result
            }else {
                this.sleep(Number(TIME_RETRY))
                return await this.retryCreateUserInFPTFaceSearch(count-1, reqData, result)
            }
        } catch (error:any) {
            this.sleep(Number(TIME_RETRY))
           return await this.retryCreateUserInFPTFaceSearch(count-1, reqData, error.response)
        }
    }

    public addFaceToFPTFaceSearchCollection = (userID: string, collectionName: string, image: any): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {
                try {
                    const reqData ={
                        userID, image, collectionName
                    }
                    const result: any = await this.retryAddFaceToFPTFaceSearchCollection(Number(COUNT_RETRY),reqData, null) // await axios.request(config)
                    if(result.status == 200){
                        logger.info(`[${process.env.FPT_FACE_SEARCH_DOMAIN}/add]: ${JSON.stringify(result.data)}`)
                        let data = result.data
                        resolve(data)
                    }else{
                        logger.error(`[${process.env.FPT_FACE_SEARCH_DOMAIN}/add]: ${JSON.stringify(result.data)}`)
                        reject({ codeALT: '210', messageErr: 'API FPT error' })
                    }
                } catch (error) {
                    return reject({ codeALT: '209', messageErr: error })
                }
            } catch (error) {
                return reject({ codeALT: '209', messageErr: error })
            }
        })
    }

    // Retry-AddFaceToFPTFaceSearchCollection
    private retryAddFaceToFPTFaceSearchCollection = async (count: number, reqData: any, returnOld?: any):Promise<any>=>{
        console.log('retryAddFaceToFPTFaceSearchCollection count: ', Number(COUNT_RETRY) - count);
        if(count <= 0){
            return returnOld 
        }
        try {
            const {userID, image, collectionName} = reqData
            let option = this.makeAddFaceToFPTFaceSearchCollectionOption(userID, collectionName, fs.createReadStream(image))
            const form = new FormData();
            form.append('collection', option.formData.collection)
            form.append('id', option.formData.id)
            form.append('file', option.formData.file)
            const config : any= {
                method: option.method,
                url: option.url,
                headers: {...option.headers,...form.getHeaders()},
                data: form
            }
            const result: any = await axios.request(config)
            if([200,201].includes(result.status)){
                return result
            }else{
                this.sleep(Number(TIME_RETRY))
                return await this.retryAddFaceToFPTFaceSearchCollection(count-1, reqData, result)
            }
        } catch (error:any) {
            this.sleep(Number(TIME_RETRY))
           return await this.retryAddFaceToFPTFaceSearchCollection(count-1, reqData, error.response)
        } 
    }

    public searchFaceInCollection = (threshold: number, collectionName: string, pathImage: any, unit: string): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {
                try {
                    const reqData ={
                        threshold, pathImage, collectionName
                    }
                    const result: any = await this.retrySearchFaceInCollection(Number(COUNT_RETRY), reqData, null) // await axios.request(config)
                    if(result?.status == 200){
                        logger.info(`[${process.env.FPT_FACE_SEARCH_DOMAIN}/search]: ${JSON.stringify(result.data)}`)
                        let data = result?.data
                        resolve(data)
                    }else{
                        logger.error(`[${process.env.FPT_FACE_SEARCH_DOMAIN}/search]: ${JSON.stringify(result.data)}`)
                        console.log(`${moment().format('DD/MM/YYYY HH:mm:ss')} `,'searchFaceInCollection err')
                        reject({ codeALT: '210', messageErr: 'API FPT error' })
                    }
                } catch (error) {
                    console.log(`${moment().format('DD/MM/YYYY HH:mm:ss')} `,'searchFaceInCollection err', error)
                    return reject({ codeALT: '201', messageErr: error })
                }
            } catch (error) {
                return reject({ codeALT: '209', messageErr: error })
            }
        })
    }

    // Retry-SearchFaceInCollection
    private retrySearchFaceInCollection = async (count: number, reqData: any, returnOld?: any):Promise<any>=>{
        console.log('retrySearchFaceInCollection count: ', Number(COUNT_RETRY) - count);
        if(count <= 0){
            return returnOld 
        }
        try {
            const {threshold, pathImage, collectionName} = reqData
            let option = this.makeFaceSearchOption(threshold, collectionName, fs.createReadStream(pathImage))
            const form = new FormData();
            form.append('collection', option.formData.collection)
            form.append('threshold', option.formData.threshold)
            form.append('file', option.formData.file)
            const config : any= {
                method:     option.method,
                url:        option.url,
                headers:    {...option.headers, ...form.getHeaders()},
                data: form
            }
            const result: any = await axios.request(config)
            if([200,201].includes(result.status)){
                return result
            }else{
                this.sleep(Number(TIME_RETRY))
                return await this.retrySearchFaceInCollection(count-1, reqData, result)
            }
        } catch (error:any) {
            this.sleep(Number(TIME_RETRY))
           return await this.retrySearchFaceInCollection(count-1, reqData, error.response)
        }  
    }

    private faceMatch = (infoReq: any, pathImageMatch: string): Promise<any> => {
        return new Promise(async (resolve, reject) => {
            try {
                const pathUser = glb_sv.getPathUser(infoReq.sec, infoReq.userId)
                const pathFrontImage = path.join(pathUser, '/front_id')
                if (!fs.existsSync(pathFrontImage)) {
                    reject({ codeALT: '103', messageErr: "Front image is invalid" })
                    return
                }
                let filenames = fs.readdirSync(pathFrontImage)
                    
                if (filenames.length == 0) {
                    return reject({ codeALT: '103', messageErr: "Front image is invalid" })
                }
                let pathImage = path.join(pathFrontImage, filenames[0])
                const result = await this.retryFaceMatch(Number(COUNT_RETRY),pathImage, pathImageMatch, null)
                if([200, 201].includes(result.status)){
                    return resolve(result.data)
                }else{
                    return reject({ codeALT: result.code, messageErr: result.data.message })
                }
            } catch (error) {
                return reject({ codeALT: '209', messageErr: error })
            }
        })
    }
    // Retry-Face Match
    private retryFaceMatch = async (count: number, pathImage: string, pathImageMatch: string, returnOld: any):Promise<any> =>{
        if(count <= 0){
            return returnOld   
        }
        const option = new FormData();
        option.append('file[]', fs.createReadStream(pathImage));
        option.append('file[]', fs.createReadStream(pathImageMatch));
        option.append('validate', '1');
        const config:any = {
            method: 'post',
            url: 'https://api.fpt.ai/dmp/checkface/v1',
            headers: {
                "Content-Type": "multipart/form-data",
                "api_key": process.env.FPT_API_KEY,
                ...option.getHeaders()
            },
            data: option
        };
        try {
            const result = await axios.request(config)
            if([200, 201].includes(result.status)){
                return result
            }else {
                await this.sleep(Number(TIME_RETRY))
                logger.info(`${moment().format('DD/MM/YYYY HH:mm:ss')} `+ 'retryFaceMatch err: '+ count)
                return await this.retryFaceMatch(count-1, pathImage, pathImageMatch, result) 
            }
        } catch (error) {
            logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `+ 'retryFaceMatch err: '+ count +" | "+JSON.stringify( error.response.data ))
            await this.sleep(Number(TIME_RETRY))
            return await this.retryFaceMatch(count-1, pathImage, pathImageMatch, error.response) 
        }       
    }
}

// public singleton call api Fpt
const insFptService = new FptService()
export default insFptService