import logger from '../../config/logger';
import databaseConnectionService from '../DatabaseConnectionService';
import { COLLECTION_OCR, OCRModel } from '../dto/OCRModel';

const insertORC = async (data: OCRModel) =>{
    try {
        const result = await databaseConnectionService.insert<OCRModel>(
            COLLECTION_OCR,
            data
        );
        logger.info(`OCRModel created with ID: ${result.insertedId}`);

    } catch (error) {
        logger.error('Error creating OCRModel:', error);
        
    }
}

export const insert_log =(result:any)=>{
    insertORC({
        name: result.data[0].name  ?? result.data[0].mrz_details.name,
        dob: result.data[0].dob ?? result.data[0].mrz_details.dob,
        sex: result.data[0].sex,
        id_card: result.data[0].id ?? result.data[0].mrz_details.id,
        nationality: result.data[0].nationality ?? result.data[0].mrz_details.nationality,
        home: result.data[0].home,
        address: result.data[0].address,
        overall_score: result.data[0].overall_score,
        checking_result: result.data[0].checking_result,
        json_data: result.data[0],
        created_at: new Date(),
        updated_at: new Date()
    });
}
export default {
    insert_log
}