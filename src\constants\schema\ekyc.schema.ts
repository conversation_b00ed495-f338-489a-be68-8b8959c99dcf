import { celebrate, Joi, errors, Segments } from 'celebrate';
import { StringSchema } from 'joi';

interface IDownloadQueryParams {
  seccode: ISecCode
  fosId: string
  target: "front_face" | "front_face_in_id" | "video" | "front_id" | "back_id"
}
interface IAddFaceToFPTParams {
  seccode: ISecCode
  fosId: string
  unit: ICurrentSupportCollection
}
interface ISearchFaceFPT {
  seccode: ISecCode
  fosId: string
  unit: ICurrentSupportCollection
  threshold: number
}

export interface IEkycSchema {
  download: IDownloadQueryParams
  addFaceToFPT: IAddFaceToFPTParams
  searchFaceFPT: ISearchFaceFPT
}

const ekycSchema = {
    checkStep: {
        [Segments.BODY]: {
          sec: Joi.string().required(),
          userId: Joi.string().required(),
        },
    },
    upload: {
        [Segments.BODY]: {
          // sec: Joi.string().required(),
          // userId: Joi.string().required(),
          // file: Joi.required(),
          // size: Joi.number().required(),
          // step: Joi.string().required(),
          // fileName: Joi.string().required()
        },
    },
    uploadOther: {
      [Segments.BODY]: {
        // sec: Joi.string().required(),
        // userId: Joi.string().required(),
        // file: Joi.required(),
        // size: Joi.number().required(),
        // step: Joi.string().required(),
        // fileName: Joi.string().required()
      },
  },
    download: {
      // [Segments.HEADERS]: {
      //   authorization: Joi.string().required()
      // },
      [Segments.QUERY]: {
        seccode: Joi.string().required(),
        fosId: Joi.string().required(),
        target: Joi.string().valid('front_face', 'front_face_in_id', 'video', 'front_id', 'back_id').required()
      }
    },
    addFaceToFPT: {
      // [Segments.HEADERS]: {
      //   authorization: Joi.string().required()
      // },
      [Segments.QUERY]: {
        seccode: Joi.string().required(),
        fosId: Joi.string().required(),
        unit: Joi.string().required()
      }
    },
  searchFaceFPT: {
      // [Segments.HEADERS]: {
      //   authorization: Joi.string().required()
      // },
      [Segments.QUERY]: {
        seccode: Joi.string().required(),
        fosId: Joi.string().required(),
        unit: Joi.string().required(),
        threshold: Joi.number().required()
      }
    },
};

export default ekycSchema