import { Request, Response, NextFunction } from 'express';
import { ChatCusTopic, ChatCusTopicMsg } from '../database/dto/ChatCusTopic';

// Utility function để check valid string
const isValidString = (value: any): boolean => {
    return typeof value === 'string' && value.trim().length > 0;
};

// Utility function để check valid number
const isValidNumber = (value: any): boolean => {
    return typeof value === 'number' && !isNaN(value);
};

// Validation cho ChatCusTopic
export const validateChatTopic = (req: Request, res: Response, next: NextFunction) => {
    const { fos_id, topic_id, topic_name }: ChatCusTopic = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(fos_id)) {
        errors.push('fos_id is required and must be a valid string');
    }
    
    // if (!isValidString(topic_id)) {
    //     errors.push('topic_id is required and must be a valid string');
    // }
    
    if (!isValidString(topic_name)) {
        errors.push('topic_name is required and must be a valid string');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho update ChatCusTopic
export const validateUpdateChatTopic = (req: Request, res: Response, next: NextFunction) => {
    const { fos_id, topic_id } = req.params;
    const updateData = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(fos_id)) {
        errors.push('fos_id parameter is required and must be a valid string');
    }
    
    if (!isValidString(topic_id)) {
        errors.push('topic_id parameter is required and must be a valid string');
    }
    
    if (Object.keys(updateData).length === 0) {
        errors.push('Update data cannot be empty');
    }
    
    // Validate specific fields if they exist in update data
    if (updateData.topic_name !== undefined && !isValidString(updateData.topic_name)) {
        errors.push('topic_name must be a valid string if provided');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho reaction update
export const validateTopicReactionUpdate = (req: Request, res: Response, next: NextFunction) => {
    const { fos_id, topic_id } = req.params;
    const { reaction } = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(topic_id)) {
        errors.push('messageId parameter is required and must be a valid string');
    }

    if (!isValidString(fos_id)) {
        errors.push('fos_id parameter is required and must be a valid string');
    }
    
    if (!isValidNumber(reaction)) {
        errors.push('reaction is required and must be a valid number');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho ChatCusTopicMsg
export const validateMessage = (req: Request, res: Response, next: NextFunction) => {
    const { topic_id, message, sender, reciever }: ChatCusTopicMsg = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(topic_id)) {
        errors.push('topic_id is required and must be a valid string');
    }
    
    if (!isValidString(message)) {
        errors.push('message is required and must be a valid string');
    }
    
    if (!isValidString(sender)) {
        errors.push('sender is required and must be a valid string');
    }
    
    if (!isValidString(reciever)) {
        errors.push('reciever is required and must be a valid string');
    }
    
    // reaction is optional, but if provided should be a number
    if (req.body.reaction !== undefined && !isValidNumber(req.body.reaction)) {
        errors.push('reaction must be a valid number if provided');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho update message
export const validateUpdateMessage = (req: Request, res: Response, next: NextFunction) => {
    const { messageId } = req.params;
    const updateData = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(messageId)) {
        errors.push('messageId parameter is required and must be a valid string');
    }
    
    if (Object.keys(updateData).length === 0) {
        errors.push('Update data cannot be empty');
    }
    
    // Validate specific fields if they exist in update data
    if (updateData.message !== undefined && !isValidString(updateData.message)) {
        errors.push('message must be a valid string if provided');
    }
    
    if (updateData.sender !== undefined && !isValidString(updateData.sender)) {
        errors.push('sender must be a valid string if provided');
    }
    
    if (updateData.reciever !== undefined && !isValidString(updateData.reciever)) {
        errors.push('reciever must be a valid string if provided');
    }
    
    if (updateData.reaction !== undefined && !isValidNumber(updateData.reaction)) {
        errors.push('reaction must be a valid number if provided');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho reaction update
export const validateReactionUpdate = (req: Request, res: Response, next: NextFunction) => {
    const { messageId } = req.params;
    const { reaction } = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(messageId)) {
        errors.push('messageId parameter is required and must be a valid string');
    }
    
    if (!isValidNumber(reaction)) {
        errors.push('reaction is required and must be a valid number');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho pagination parameters với sort
export const validatePagination = (req: Request, res: Response, next: NextFunction) => {
    const { page, limit, sort, sortOrder } = req.query;

    const errors: string[] = [];

    if (page && (isNaN(Number(page)) || Number(page) < 1)) {
        errors.push('page must be a positive number');
    }

    if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
        errors.push('limit must be a positive number and not exceed 100');
    }

    // Validate sort parameter
    if (sort && typeof sort !== 'string') {
        errors.push('sort must be a string');
    }

    // Validate sortOrder parameter
    if (sortOrder && !['asc', 'desc'].includes(sortOrder as string)) {
        errors.push('sortOrder must be either "asc" or "desc"');
    }

    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }

    next();
};

// Validation cho path parameters
export const validatePathParams = (req: Request, res: Response, next: NextFunction) => {
    const { fos_id, topic_id, topicId, messageId } = req.params;
    
    const errors: string[] = [];
    
    if (fos_id && !isValidString(fos_id)) {
        errors.push('fos_id parameter must be a valid string');
    }
    
    if (topic_id && !isValidString(topic_id)) {
        errors.push('topic_id parameter must be a valid string');
    }
    
    if (topicId && !isValidString(topicId)) {
        errors.push('topicId parameter must be a valid string');
    }
    
    if (messageId && !isValidString(messageId)) {
        errors.push('messageId parameter must be a valid string');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};
