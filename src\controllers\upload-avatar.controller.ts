import IController from 'IController';
import httpStatusCodes from 'http-status-codes';
import apiResponse from '../utilities/apiResponse';
import locale from '../constants/locale';
import * as process from 'process';
import logger from '../config/logger';
import avatarService from '../services/AvatarService';

const hashSHA256 = process.env.AVATAR_UPLOAD_HEADER || '85680a1aeb82a95c561838e9bb93102872fdbdc59a9b4e69d162c0dd1e241fb9' // thisisthekeyforuploadavatar
const uploadAvatar: IController = async (req, res) => {
  try {
    const { headers } = req;
    if (!headers['x-upload-avatar-x']
        || (headers['x-upload-avatar-x']
        && headers['x-upload-avatar-x'] !== hashSHA256)
    ) {
      apiResponse.error(
          res,
          httpStatusCodes.FORBIDDEN,
      );
      return
    }

    const result = await avatarService.uploadAvatar(req)
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        err.message,
    );
  }
}

const getAvatar: IController = async (req, res) => {
  const errorMessage = 'Invalid request.';
  try {
    const { query, headers } = req;
    if (!headers['x-upload-avatar-x']
        || (headers['x-upload-avatar-x']
        && headers['x-upload-avatar-x'] !== hashSHA256)
    ) {
      apiResponse.error(
          res,
          httpStatusCodes.FORBIDDEN,
      );
      return
    }

    const { accNo } = query

    if (!accNo || accNo.toString().trim().length  === 0) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          errorMessage,
      );
      return
    }

    const fileNamePattern = /^\d{10,}$/;
    if (!fileNamePattern.test(accNo.toString())) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          errorMessage,
      );
      return
    }

    const result = await avatarService.getAvatar(accNo.toString())

    if (!result.success) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          result['message'],
      );
      return
    }

    apiResponse.result(res, { result });

    logger.info('-----------------End get avatar-----------------------');
  } catch (error) {
    logger.info('----------------Error End get avatar-----------------------');
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
}

const uploadGenerateAvatar: IController = async (req, res) => { 
   try {
    const { headers } = req;
    if (!headers['x-upload-avatar-x'] || (headers['x-upload-avatar-x'] && headers['x-upload-avatar-x'] !== hashSHA256)) {
      apiResponse.error(
          res,
          httpStatusCodes.FORBIDDEN,
      );
      return
    }
    logger.info('-----------------Start Upload Generate Avatar-----------------------');
    const result = await avatarService.uploadGenerateAvatar(req)
    apiResponse.result(res, result);
    logger.info('-----------------End Upload Generate Avatar-----------------------');
   } catch (error) {
    logger.info('----------------Error End Upload Generate Avatar-----------------------');
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
   }
}

export default { uploadAvatar, getAvatar, uploadGenerateAvatar }
