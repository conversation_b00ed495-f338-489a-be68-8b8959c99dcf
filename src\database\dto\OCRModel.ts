
// collection name
export const COLLECTION_OCR = 'ocr_card_info';

export type IDCardCombinedModel = IDCardInfoModel | ChipBackModel;

// Interface cho OCR
export interface OCRModel {
    _id?: string;
    id_card?: string;
    name: string;
    dob: string;
    sex: string;
    nationality: string;
    home: string;
    address: string;
    overall_score: string;
    checking_result: string;
    json_data: Record<string, any>; // hoặc `any`
    created_at: Date;
    updated_at: Date;
} 

export interface IDCardInfoModel {
  id: string;
  id_prob: string;
  name: string;
  name_prob: string;
  dob: string;
  dob_prob: string;
  sex: string;
  sex_prob: string;
  nationality: string;
  nationality_prob: string;
  overall_score: string;
  number_of_name_lines: string;
  home: string;
  home_prob: string;
  address: string;
  address_prob: string;
  doe: string;
  doe_prob: string;
  type_new: string;
  type: string;
  checking_result: CheckingResult;
  face_coords: string;
  face_base64: string;
  cropped_idcard_base64: string;
  errorMessage: string[];
  errorCode: number;
  codeALT: string;
  success: boolean;
}

export interface CheckingResult {
  low_score_result: any[];  // bạn có thể thay bằng kiểu cụ thể nếu biết
  recaptured_result: string;
  recaptured_prob: string;
  check_watermark_result: string;
  check_watermark_prob: string;
  edited_result: string;
  edited_prob: string;
  corner_cut_result: string;
  corner_cut_prob: string[];
  check_covering_result: string;
  check_covering_prob: string;
  check_border_result: string;
  check_border_prob: string;
  check_emblem_result: string;
  check_emblem_prob: string;
  check_headline_result: string;
  check_headline_prob: string;
  check_title_result: string;
  check_title_prob: string;
  check_QRcode_result: string;
  check_QRcode_prob: string;
  check_fingerprint_result: string;
  check_fingerprint_prob: string;
  check_stamp_result: string;
  check_stamp_prob: string;
  check_chip_result: string;
  check_chip_prob: string;
  check_mrz_result: string;
  check_mrz_prob: string;
  check_embossed_stamp_result: string;
  check_embossed_stamp_prob: string;
  check_barcode_result: string;
  check_barcode_prob: string;
  check_anti_counterfeiting_stamps_result: string;
  check_anti_counterfeiting_stamps_prob: string;
  check_photocopied_result: string;
  check_photocopied_prob: string;
  check_face_result: string;
  check_face_prob: string;
}

// Back-ID
export interface ChipBackModel {
  features: string;
  features_prob: string;
  issue_date: string;
  issue_date_prob: string;
  mrz: string[];
  mrz_prob: string;
  overall_score: string;
  issue_loc: string;
  issue_loc_prob: string;
  type_new: string;
  type: string;
  mrz_details: MrzDetails;
  checking_result: ChipBackCheckingResult;
  pob: string;
  pob_prob: string;
  address: string;
  address_prob: string;
  doe: string;
  doe_prob: string;
  face_base64: string;
  cropped_idcard_base64: string;
  errorMessage: string[];
  errorCode: number;
  codeALT: string;
  success: boolean;
}

export interface MrzDetails {
  id: string;
  name: string;
  doe: string;
  dob: string;
  nationality: string;
  sex: string;
}

export interface ChipBackCheckingResult {
  low_score_result: any[];  // or string[] if known
  recaptured_result: string;
  recaptured_prob: string;
  check_watermark_result: string;
  check_watermark_prob: string;
  edited_result: string;
  edited_prob: string;
  corner_cut_result: string;
  corner_cut_prob: string[];
  check_covering_result: string;
  check_covering_prob: string;
  check_border_result: string;
  check_border_prob: string;
  check_fingerprint_result: string;
  check_fingerprint_prob: string[]; // it returns ["1.00", "1.00"]
  check_stamp_result: string;
  check_stamp_prob: string;
  check_chip_result: string;
  check_chip_prob: string;
  check_mrz_result: string;
  check_mrz_prob: string;
  check_emblem_result: string;
  check_emblem_prob: string;
  check_headline_result: string;
  check_headline_prob: string;
  check_title_result: string;
  check_title_prob: string;
  check_QRcode_result: string;
  check_QRcode_prob: string;
  check_embossed_stamp_result: string;
  check_embossed_stamp_prob: string;
  check_barcode_result: string;
  check_barcode_prob: string;
  check_anti_counterfeiting_stamps_result: string;
  check_anti_counterfeiting_stamps_prob: string;
  check_photocopied_result: string;
  check_photocopied_prob: string;
  check_face_result: string;
  check_face_prob: string;
}
