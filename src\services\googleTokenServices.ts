const request = require('request')
const fs = require('fs')
import logger from '../config/logger';

const SITE_URL = process.env.SITE_URL || "https://www.google.com/recaptcha/api/siteverify"
const SECRET_KEY = process.env.SECRET_KEY || "6LfDAXEnAAAAAEpN2wNawGtx9Ojl-sRIA3Ci7eJf"

class GoogleTokenService {  
    public callGoogleToken = (data :any) =>
    {
        const host  =`${SITE_URL}?secret=${SECRET_KEY}&response=${data?.recaptcha_token}`       
        return new Promise((resolve, reject) => {
            try {
                const config = {
                    method: 'post', 
                    url: host,
                    headers: {
                        "Content-Type": "apllication/json",
                    }, 
                }; 
                request(config, function (err: any, res: any, body: any) {
                    if (err) {
                        return reject({ codeALT: '201', messageErr: err })
                    }
                    logger.info(`==============res===============`,res.body)
                    try {
                        const jsonParse = JSON.parse(body)
                        resolve(jsonParse)
                    } catch {
                        reject({ codeALT: '210', messageErr: 'API GOOGLE TOKEN error' })
                    }
                }) 
            }catch (error){
                return reject({ codeALT: '209', messageErr: error })

            }
        })        
    }
}

// public singleton call api
const googleTokenService = new GoogleTokenService()
export default googleTokenService