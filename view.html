<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <title></title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.3/moment.min.js" crossorigin="anonymous"
        referrerpolicy="no-referrer"></script>
</head>

<body>

    <br />
    <input type="file" id="my-file" multiple />
    <br />
    <!-- <input type="file" id="upload-file" />
    <br /> -->


    <!-- <video id="video_parent" controls autoplay name="media">
        <source id="video_src" src="" type="video/mp4">
    </video> -->

    <script type="text/javascript">

        var file = document.getElementById('my-file');

        file.addEventListener('change', function () {
            if (!file.files.length) {
                return;
            }

            // var firstFileFace = file.files[0];
            var firstFile = file.files[0];
            let formData = new FormData()
            formData.append('sec', '036')
            formData.append('userId', '00234')
            formData.append('file', firstFile)
            formData.append('fileName', firstFile.name)
            // formData.append('fileFace', firstFileFace)
            // formData.append('fileNameFace', firstFileFace.name)
            formData.append('step', '1')
            // formData.append('size', 10000)
            // formData.append('isImageFaceMatch', 0)
            console.log('start', formData, firstFile)
            fetch('http://localhost:8005/api/ekyc/upload-other', {
                method: "POST",
                body: formData
            })
                .then(res => res.json())
                .then(res => console.log('end: ', res))
                .catch(function (res) { console.log('error: ', res) })
        });

        // var uploadFile = document.getElementById('upload-file');

        // uploadFile.addEventListener('change', function () {
        //     console.log('uploadFile.files', uploadFile.files)
        //     return
        //     if (!uploadFile.files.length) {
        //         return;
        //     }

        //     var firstFile = uploadFile.files[0];
        //     let formData = new FormData()
        //     formData.append('file', firstFile)
        //     formData.append('fileName', firstFile.name)
        //     formData.append('fileType', firstFile.type)
        //     console.log('start', formData)
        //     fetch('https://ekyc.shinhansec.com.vn/api/upload', {
        //         method: "POST",
        //         body: formData
        //     })
        //         .then(res => res.json())
        //         .then(res => console.log('end: ', res))
        //         .catch(function (res) { console.log('error: ', res) })
        // });
    </script>
    <script type="text/javascript">
        // console.log('start check-step')
        // const start = moment()
        // fetch('https://testekyc.shinhansec.com.vn/api/ekyc/check-step', {
        //     headers: {
        //         'Accept': 'application/json',
        //         'Content-Type': 'application/json'
        //     },
        //     method: "POST",
        //     body: JSON.stringify({
        //         "sec": "888",
        //         "userId": "0000037924"
        //     })
        // })
        //     .then(res => res.json())
        //     .then(res => console.log('res: ', moment() - start, res))
        //     .catch(function (res) { console.log('error: ', res) })

        // fetch('http://localhost:8005/api/upload/delete', {
        //     headers: {
        //         'Accept': 'application/json',
        //         'Content-Type': 'application/json'
        //     },
        //     method: "POST",
        //     body: JSON.stringify({
        //         "files": ['f54ec90b-00b8-4099-9777-f82145a0acbb.jpg', '85586987-3870-4256-85d8-0e33d923f2d1.jpg', '52eff539-2472-448a-a9b4-e066ef0c59a9.png']
        //     })
        // })
        //     .then(res => res.json())
        //     .then(res => console.log('res: ', moment() - start, res))
        //     .catch(function (res) { console.log('error: ', res) })
    </script>
</body>

</html>