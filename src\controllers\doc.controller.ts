import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insDocService from '../services/DocService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';


const callGetDoc: IController = async (req, res) => {
    try {
        let { lang } = req.body;
        console.log(req.body);
        logger.info('lang input >>> ' + lang);
        console.log(typeof lang);

        if (typeof lang === "undefined" || !lang ) {
            logger.info('lang check input false >>> ' + lang);
            return apiResponse.result(res, {  successDoc:false, url: "" });
        } else {
            const resDocService = await insDocService.returnUrlDoc(lang);
            return apiResponse.result(res, { successDoc:true, url: resDocService });
        }
    } catch (e) {
        logger.info('Error >>> ' + JSON.stringify(e));
        return apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }

};

export default {
    callGetDoc,
};