import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insBankService from '../services/BankService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import fs from 'fs';
import path from 'path';
import logger from '../config/logger';

const callGetImage: IController = async (req, res) => {
    let {idCard} = req.body;

    if(!idCard) {
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );

    } else {
        try {
            const resImg = await insBankService.getImage(req);

            console.log(resImg);

            apiResponse.result(res, { response: resImg });
        } catch (e) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    }

}

const callUpdateStatusAcc: IController = async (req, res) => {
    let {acStatus, trxKey} = req.body;

    if(!acStatus || !trxKey) {
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );

    } else {
        try {
            const resUpdateAcc = await insBankService.updateStatus(req);

            console.log(resUpdateAcc);

            apiResponse.result(res, { response: resUpdateAcc });
        } catch (e) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    }

}

// lấy list -> (id, file1, file2, file3) -> cập nhật trạng thái 
const callCreateFileZip: IController = async (req, res) => {
    // lấy ảnh + file nén lại rồi gửi đi
    let {onboarding_id, file1, file2, file3} = req.body;
    if(!onboarding_id || !file1 || !file2 || !file3) { 
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );

    } else {
        try {``
            // tạo folder sol
            const PATH_IMAGE  = process.env.PATH_IMAGE ?? "temp-upload/newFOS/";
            let path_image =  path.join(process.cwd(), PATH_IMAGE);
            // xóa dữ liệu sol + onboarding_id  và zip
            if (fs.existsSync(path.join(process.cwd(),"temp-upload/sol/" +onboarding_id))) {
                fs.rmSync(path.join(process.cwd(),"temp-upload/sol/" +onboarding_id), { recursive: true, force: true });
            }
            if (fs.existsSync(path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id))) {
                fs.rmSync(path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id), { recursive: true, force: true });
            }

            // kiểm tra exist folder
            if (!fs.existsSync(path.join(process.cwd(),"temp-upload/sol"))) {
                fs.mkdirSync(path.join(process.cwd(),"temp-upload/sol"));
            }
            if (!fs.existsSync(path.join(process.cwd(),"temp-upload/sol/zip"))) {
                fs.mkdirSync(path.join(process.cwd(),"temp-upload/sol/zip"));
            }
            if (!fs.existsSync(path.join(process.cwd(),"temp-upload/sol/" +onboarding_id))) {
                fs.mkdirSync(path.join(process.cwd(),"temp-upload/sol/" +onboarding_id));
            }
            if (!fs.existsSync(path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id))) {
                fs.mkdirSync(path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id));
            }
            let time_excute = Date.now();
            
            let file_path = path.join(process.cwd(),"temp-upload/sol/" +onboarding_id);
            let file_path_zip = path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id);
            let file_name_txt = "SHB_"+onboarding_id+"_"+time_excute+".txt";
            let file_name_zip = "SHB_"+onboarding_id+"_"+time_excute+".zip";

            // 1. tạo file txt
            let input_file = "";
            let list_image = [];
            if(file1){
                list_image.push(file1)
                input_file = onboarding_id + "|01|" + file1 +"|\n";               
            }

            if(file2){
                list_image.push(file2)    
                input_file += onboarding_id + "|02|" + file2 +"|\n";    
            } 

            if(file3) {
                list_image.push(file3)    
                input_file += onboarding_id + "|03|" + file3 +"|\n";    
            }

            fs.writeFileSync(file_path + "/"+ file_name_txt, input_file);
            // 2. Biến env path lấy hình
            if(list_image.length>0){
                let error =[];
                for(let i = 0; i < list_image.length; i++){
                   let rs = await insBankService.copyImageSol(file_path, path_image, list_image[i]);
                    if(rs ==false)error.push(rs)
                }
                if(error.length > 0){
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.API_ALT_ERROR,
                    );
                    return;
                }
            }

            // 3 zip folder
            logger.info('file_path_zip: ' + file_path_zip);
            const out = path.join(file_path_zip, file_name_zip);
            const resultZip = await insBankService.zipDirectory(file_path, out);
            if(resultZip){
                apiResponse.result(res,{ data:{ filename: file_name_zip, path: file_path_zip } });
            }else{
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.API_ALT_ERROR,
                );
            }
            return;
        } catch (e) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    }
    return 
}

const callGetFileZip :IController = async (req, res) =>{
    let {file_name_zip, onboarding_id} = req.body;
    if(!file_name_zip || !onboarding_id) { 
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
    }

    try {
        const path_zip =  path.join(process.cwd(),"temp-upload/sol/zip/" +onboarding_id);
        // check exist file
        if (fs.existsSync(path.join(path_zip, file_name_zip))) {
            res.download(path.join(path_zip, file_name_zip));
        } else {
            apiResponse.error(
                res,
                httpStatusCodes.NOT_FOUND,
                locale.FILE_NOT_FOUND,
            );
        }
    } catch (error) {
         apiResponse.error(
            res,
            httpStatusCodes.BAD_GATEWAY,
            locale.REQUEST_ERROR,
        );
    }
    return
}
export default {
    callGetImage,
    callUpdateStatusAcc,
    callCreateFileZip,
    callGetFileZip
}