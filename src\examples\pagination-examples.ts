// Examples demonstrating enhanced pagination features
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

// ============ Enhanced Pagination Examples ============

// 1. Basic pagination
export const basicPaginationExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: {
                page: 1,
                limit: 10
            }
        });
        
        console.log('Basic pagination result:');
        console.log('- Current page:', response.data.data.pagination.currentPage);
        console.log('- Total pages:', response.data.data.pagination.totalPages);
        console.log('- Total items:', response.data.data.pagination.totalItems);
        console.log('- Has next page:', response.data.data.pagination.hasNextPage);
        console.log('- Has prev page:', response.data.data.pagination.hasPrevPage);
        
        return response.data;
    } catch (error) {
        console.error('Error in basic pagination:', error.response?.data);
        throw error;
    }
};

// 2. Pagination with sorting
export const paginationWithSortingExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: {
                page: 1,
                limit: 5,
                sort: 'topic_name',
                sortOrder: 'asc'
            }
        });
        
        console.log('Pagination with sorting result:');
        console.log('- Sort field:', response.data.data.pagination.sort);
        console.log('- Sort order:', response.data.data.pagination.sortOrder);
        console.log('- Items count:', response.data.data.data.length);
        
        // Show first few topic names to verify sorting
        response.data.data.data.slice(0, 3).forEach((topic: any, index: number) => {
            console.log(`- Topic ${index + 1}:`, topic.topic_name);
        });
        
        return response.data;
    } catch (error) {
        console.error('Error in pagination with sorting:', error.response?.data);
        throw error;
    }
};

// 3. Advanced pagination navigation
export const paginationNavigationExample = async () => {
    try {
        console.log('=== Pagination Navigation Demo ===');
        
        // Get first page
        const firstPage = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: { page: 1, limit: 3 }
        });
        
        console.log('First page:');
        console.log('- Current page:', firstPage.data.data.pagination.currentPage);
        console.log('- Next page:', firstPage.data.data.pagination.nextPage);
        console.log('- Has next page:', firstPage.data.data.pagination.hasNextPage);
        
        // Navigate to next page if available
        if (firstPage.data.data.pagination.hasNextPage) {
            const nextPageNum = firstPage.data.data.pagination.nextPage;
            const nextPage = await axios.get(`${API_BASE_URL}/chat-topics`, {
                params: { page: nextPageNum, limit: 3 }
            });
            
            console.log('\nNext page:');
            console.log('- Current page:', nextPage.data.data.pagination.currentPage);
            console.log('- Previous page:', nextPage.data.data.pagination.prevPage);
            console.log('- Next page:', nextPage.data.data.pagination.nextPage);
            console.log('- Has prev page:', nextPage.data.data.pagination.hasPrevPage);
            console.log('- Has next page:', nextPage.data.data.pagination.hasNextPage);
        }
        
        return { firstPage: firstPage.data };
    } catch (error) {
        console.error('Error in pagination navigation:', error.response?.data);
        throw error;
    }
};

// 4. Messages pagination by topic
export const messagesPaginationExample = async () => {
    try {
        const topicId = 'TOPIC001';
        
        const response = await axios.get(`${API_BASE_URL}/messages/topic/${topicId}`, {
            params: {
                page: 1,
                limit: 20,
                sort: 'created_at',
                sortOrder: 'desc' // Newest messages first
            }
        });
        
        console.log('Messages pagination result:');
        console.log('- Topic ID:', topicId);
        console.log('- Messages count:', response.data.data.data.length);
        console.log('- Total messages:', response.data.data.pagination.totalItems);
        console.log('- Sort order:', response.data.data.pagination.sortOrder);
        
        // Show message timestamps to verify sorting
        response.data.data.data.slice(0, 3).forEach((message: any, index: number) => {
            console.log(`- Message ${index + 1} created:`, message.created_at);
        });
        
        return response.data;
    } catch (error) {
        console.error('Error in messages pagination:', error.response?.data);
        throw error;
    }
};

// 5. Filter with pagination
export const filterWithPaginationExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: {
                page: 1,
                limit: 10,
                sort: 'updated_at',
                sortOrder: 'desc',
                fos_id: 'FOS001' // Filter by specific FOS ID
            }
        });
        
        console.log('Filter with pagination result:');
        console.log('- Filtered by fos_id: FOS001');
        console.log('- Results count:', response.data.data.data.length);
        console.log('- Total matching items:', response.data.data.pagination.totalItems);
        
        return response.data;
    } catch (error) {
        console.error('Error in filter with pagination:', error.response?.data);
        throw error;
    }
};

// 6. Large dataset pagination
export const largeDatasetPaginationExample = async () => {
    try {
        console.log('=== Large Dataset Pagination Demo ===');
        
        // Get total count first
        const firstPage = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: { page: 1, limit: 1 }
        });
        
        const totalItems = firstPage.data.data.pagination.totalItems;
        const totalPages = firstPage.data.data.pagination.totalPages;
        
        console.log('Dataset overview:');
        console.log('- Total items:', totalItems);
        console.log('- Total pages:', totalPages);
        
        // Jump to middle page
        const middlePage = Math.ceil(totalPages / 2);
        if (middlePage > 1) {
            const middlePageData = await axios.get(`${API_BASE_URL}/chat-topics`, {
                params: { page: middlePage, limit: 10 }
            });
            
            console.log('\nMiddle page navigation:');
            console.log('- Jumped to page:', middlePage);
            console.log('- Items on this page:', middlePageData.data.data.data.length);
            console.log('- Can go back:', middlePageData.data.data.pagination.hasPrevPage);
            console.log('- Can go forward:', middlePageData.data.data.pagination.hasNextPage);
        }
        
        // Jump to last page
        if (totalPages > 1) {
            const lastPageData = await axios.get(`${API_BASE_URL}/chat-topics`, {
                params: { page: totalPages, limit: 10 }
            });
            
            console.log('\nLast page navigation:');
            console.log('- Last page number:', totalPages);
            console.log('- Items on last page:', lastPageData.data.data.data.length);
            console.log('- Has previous page:', lastPageData.data.data.pagination.hasPrevPage);
            console.log('- Has next page:', lastPageData.data.data.pagination.hasNextPage);
        }
        
        return { totalItems, totalPages };
    } catch (error) {
        console.error('Error in large dataset pagination:', error.response?.data);
        throw error;
    }
};

// 7. Pagination performance comparison
export const paginationPerformanceExample = async () => {
    try {
        console.log('=== Pagination Performance Comparison ===');
        
        // Small page size
        const startTime1 = Date.now();
        const smallPages = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: { page: 1, limit: 5 }
        });
        const time1 = Date.now() - startTime1;
        
        // Large page size
        const startTime2 = Date.now();
        const largePages = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: { page: 1, limit: 50 }
        });
        const time2 = Date.now() - startTime2;
        
        console.log('Performance comparison:');
        console.log(`- Small page (5 items): ${time1}ms`);
        console.log(`- Large page (50 items): ${time2}ms`);
        console.log(`- Performance difference: ${time2 - time1}ms`);
        
        return { smallPageTime: time1, largePageTime: time2 };
    } catch (error) {
        console.error('Error in pagination performance test:', error.response?.data);
        throw error;
    }
};

// 8. Complete pagination workflow
export const completePaginationWorkflow = async () => {
    try {
        console.log('=== Complete Pagination Workflow ===');
        
        // 1. Basic pagination
        console.log('1. Testing basic pagination...');
        await basicPaginationExample();
        
        // 2. Sorting
        console.log('\n2. Testing pagination with sorting...');
        await paginationWithSortingExample();
        
        // 3. Navigation
        console.log('\n3. Testing pagination navigation...');
        await paginationNavigationExample();
        
        // 4. Messages pagination
        console.log('\n4. Testing messages pagination...');
        await messagesPaginationExample();
        
        // 5. Filtering
        console.log('\n5. Testing filter with pagination...');
        await filterWithPaginationExample();
        
        console.log('\n=== All pagination tests completed successfully ===');
        
    } catch (error) {
        console.error('Pagination workflow failed:', error);
        throw error;
    }
};

// Utility function to display pagination info
export const displayPaginationInfo = (paginationData: any) => {
    console.log('📄 Pagination Info:');
    console.log(`   Current Page: ${paginationData.currentPage}/${paginationData.totalPages}`);
    console.log(`   Items: ${paginationData.itemsPerPage} per page, ${paginationData.totalItems} total`);
    console.log(`   Navigation: ${paginationData.hasPrevPage ? '← Prev' : ''} ${paginationData.hasNextPage ? 'Next →' : ''}`);
    console.log(`   Sort: ${paginationData.sort} (${paginationData.sortOrder})`);
};
