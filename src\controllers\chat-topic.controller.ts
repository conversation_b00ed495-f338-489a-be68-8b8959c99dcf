import { Request, Response } from 'express';
import chatHistoryService from '../database/services/chat-history.service';
import chatTopicMsgService from '../database/services/chat-topic-msg.service';
import { ChatCusTopic, ChatCusTopicMsg } from '../database/dto/ChatCusTopic';

// ============ ChatCusTopic Controllers ============

// Tạo chat topic mới
export const createChatTopic = async (req: Request, res: Response) => {
    try {
        const chatData: ChatCusTopic = req.body;
        const result = await chatHistoryService.createChat(chatData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Chat topic created successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật chat topic
export const updateChatTopic = async (req: Request, res: Response) => {
    try {
        const { fos_id, topic_id } = req.params;
        const updateData: Partial<ChatCusTopic> = req.body;
        
        const result = await chatHistoryService.updateChat(fos_id, topic_id, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topic updated successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Xóa chat topic
export const deleteChatTopic = async (req: Request, res: Response) => {
    try {
        const { fos_id, topic_id } = req.params;
        
        const result = await chatHistoryService.deleteChat(fos_id, topic_id);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topic deleted successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy danh sách chat topics với filter và phân trang
export const getChatTopics = async (req: Request, res: Response) => {
    try {
        const { page = 1, limit = 10, ...filter } = req.query;
        
        const filterData = {
            filter: filter,
            page: parseInt(page as string),
            limit: parseInt(limit as string)
        };
        
        const result = await chatHistoryService.getChat(filterData);
        
        res.status(200).json({
            success: true,
            message: 'Chat topics retrieved successfully',
            data: result.data
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy chat topic theo ID
export const getChatTopicById = async (req: Request, res: Response) => {
    try {
        const { fos_id, topic_id } = req.params;
        
        const result = await chatHistoryService.getChatById(fos_id, topic_id);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topic retrieved successfully',
                data: result.data
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy chat topics theo fos_id
export const getChatTopicsByFosId = async (req: Request, res: Response) => {
    try {
        const { fos_id } = req.params;
        const { page = 1, limit = 10 } = req.query;
        
        const result = await chatHistoryService.getChatsByFosId(
            fos_id, 
            parseInt(page as string), 
            parseInt(limit as string)
        );
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topics retrieved successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// ============ ChatCusTopicMsg Controllers ============

// Tạo message mới
export const createMessage = async (req: Request, res: Response) => {
    try {
        const messageData: ChatCusTopicMsg = req.body;
        const result = await chatTopicMsgService.createMessage(messageData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Message created successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật message
export const updateMessage = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        const updateData: Partial<ChatCusTopicMsg> = req.body;
        
        const result = await chatTopicMsgService.updateMessage(messageId, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message updated successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Xóa message
export const deleteMessage = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        
        const result = await chatTopicMsgService.deleteMessage(messageId);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message deleted successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy messages theo topic_id
export const getMessagesByTopicId = async (req: Request, res: Response) => {
    try {
        const { topicId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        
        const result = await chatTopicMsgService.getMessagesByTopicId(
            topicId, 
            parseInt(page as string), 
            parseInt(limit as string)
        );
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Messages retrieved successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật reaction cho message
export const updateMessageReaction = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        const { reaction } = req.body;
        
        const result = await chatTopicMsgService.updateMessageReaction(messageId, reaction);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message reaction updated successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};
