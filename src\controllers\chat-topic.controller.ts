import { Request, Response } from 'express';
import chatHistoryService from '../database/services/chat-history.service';
import chatTopicMsgService from '../database/services/chat-topic-msg.service';
import { ChatCusTopic, ChatCusTopicMsg } from '../database/dto/ChatCusTopic';

// ============ ChatCusTopic Controllers ============

// Tạo chat topic mới
export const createChatTopic = async (req: Request, res: Response) => {
    try {
        const chatData: ChatCusTopic = req.body;
        const result = await chatHistoryService.createChat(chatData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Chat topic created successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật chat topic
export const updateChatTopic = async (req: Request, res: Response) => {
    try {
        const { fos_id, topic_id } = req.params;
        const updateData: Partial<ChatCusTopic> = req.body;
        
        const result = await chatHistoryService.updateChat(fos_id, topic_id, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topic updated successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Xóa chat topic
export const deleteChatTopic = async (req: Request, res: Response) => {
    try {
        const { fos_id, topic_id } = req.params;
        
        const result = await chatHistoryService.deleteChat(fos_id, topic_id);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Chat topic deleted successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy danh sách chat topics với filter và phân trang cải tiến
export const getChatTopics = async (req: Request, res: Response) => {
    try {
        const {
            page = 1,
            limit = 10,
            sort = 'created_at',
            sortOrder = 'desc',
            ...filter
        } = req.query;
        if(!req.query?.fos_id){
             res.status(400).json({
                success: false,
                message: 'Input fos_id is required',
            });
            return;
        }
        const filterData = {
            filter: filter,
            page: parseInt(page as string),
            limit: Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort: sort as string,
            sortOrder: (sortOrder as string) === 'asc' ? 'asc' as const : 'desc' as const
        };

        const result = await chatHistoryService.getChat(filterData);

        res.status(200).json({
            success: true,
            message: 'Chat topics retrieved successfully',
            data: result.data
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};


// ============ ChatCusTopicMsg Controllers ============

// Tạo message mới
export const createMessage = async (req: Request, res: Response) => {
    try {
        const messageData: ChatCusTopicMsg = req.body;
        const result = await chatTopicMsgService.createMessage(messageData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Message created successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật message
export const updateMessage = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        const updateData: Partial<ChatCusTopicMsg> = req.body;        
        const result = await chatTopicMsgService.updateMessage(messageId, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message updated successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Xóa message
export const deleteMessage = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        const { fos_id, topic_id } = req.query;
        const result = await chatTopicMsgService.deleteMessage(messageId, fos_id.toString(), topic_id.toString());
    
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message deleted successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy messages theo topic_id với phân trang cải tiến
export const getMessagesByTopicId = async (req: Request, res: Response) => {
    try {
        const { topicId } = req.params;
        const {
            fos_id,
            page = 1,
            limit = 10,
            sort = 'created_at',
            sortOrder = 'desc'
        } = req.query;

        const result = await chatTopicMsgService.getMessagesByTopicId(
            fos_id.toString(),
            topicId,
            parseInt(page as string),
            Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort as string,
            (sortOrder as string) === 'asc' ? 'asc' : 'desc'
        );

        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Messages retrieved successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật reaction cho message
export const updateMessageReaction = async (req: Request, res: Response) => {
    try {
        const { messageId } = req.params;
        const { reaction, fos_id, topic_id } = req.body;
        
        const result = await chatTopicMsgService.updateMessageReaction(fos_id, topic_id, messageId, reaction);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Message reaction updated successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật reaction cho Topic
export const updateTopicReaction = async (req: Request, res: Response) => {
    try {
        const { topic_id, fos_id} = req.params;
        const { reaction } = req.body;
        const result = await chatHistoryService.updateTopicReaction(fos_id, topic_id, reaction);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Topic reaction updated successfully',
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        console.log('error: ', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

