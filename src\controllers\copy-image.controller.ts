import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insCopyImageService from '../services/CopyImageService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';



const copyFileFolder: IController = async (req, res) => {
    try {
        const { body,  }= req;

        const result = await insCopyImageService.uploadToServer(req);
        logger.info(JSON.stringify(result));
        apiResponse.result(res, { responseServer: 'ok', response: result });
    } catch (e) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
}

const createEcontract: IController = async (req, res) => {
    try {
        const { body,  }= req;
        logger.info("|----------------------------- START: createEcontract -----------------------------|");
        logger.info(`| - PATH: '/createEcontract' SERVICES: createEcontract`)
        logger.info(`| - INPUT: ${JSON.stringify(body)}`)
        const result = await insCopyImageService.callSyncCreateEcontract(req);
        logger.info(`| - RESULT: ${JSON.stringify(result)}`)
        apiResponse.result(res, { responseServer: 'ok', response: result });
    } catch (e) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            e.messageErr,
        );
    }finally{
        logger.info("|-------------------------------- END ---------------------------------------------|");
    }
}

export default {
    copyFileFolder,
    createEcontract
}
