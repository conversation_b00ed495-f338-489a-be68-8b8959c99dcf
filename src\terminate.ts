﻿import logger from "./config/logger";

export function terminate(server: any, options = { coredump: false, timeout: 500 }) {
    logger.info({ message: "function terminate", server, options })
    // Exit function
    const exit = (code: any) => {
        options.coredump ? process.abort() : process.exit(code)
    }

    return (code: any, reason: any) => (err: any, promise: any) => {
        logger.error({ message: "[terminate return]: ", code, reason, err });
        
        if (err && err instanceof Error) {
            // Log error information, use a proper logging library here :)
            console.log(err.message, err.stack)
            logger.error({ message: "[terminate return]: ", "err.message": err.message, "err.stack": err.stack });
        }

        // Attempt a graceful shutdown
        server.close(exit)
        setTimeout(exit, options.timeout).unref()
    }
}

