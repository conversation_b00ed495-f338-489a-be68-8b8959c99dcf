
/**
 * TODO: <PERSON><PERSON><PERSON> thông tin nếu có thay đổi phải báo backend biết
 */
const getCompanyNameBySeccode = (seccode: string): ICompanyNameUpper | 'ERR' => {
    if (seccode === '888') return 'ALTISSS'
    if (seccode === '081') return 'SSV'
    if (seccode === '028') return 'NSI'
    if (seccode === '075') return 'BETA'
    if (seccode === '061') return 'GTJAI'
    if (seccode === '036') return 'APSC'
    if (seccode === '020') return 'VISE'
    if (seccode === '004') return 'YSVN'
    if (seccode === '102') return 'VNCSI'
    if (seccode === '023') return 'VIETSC'
    if (seccode === '082') return 'HBSE'
    if (seccode === '099') return 'ASAM'

    return 'ERR'
}

export const createFaceSearchCollectionID = (seccode: string) => {
    return `${process.env.ENV === 'dev' ? 'UAT' : 'PRO'}_${seccode}_${getCompanyNameBySeccode(seccode)}`
}
