import IRequest from 'IRequest';
import logger from '../config/logger';
import process from 'process';
import axios from 'axios';

const stockMarketUrl = process.env.INFORBIP_URL;
class StockMarketService {
  public getStockMarket = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr, rejectPr) => {
        // console.log(req.body, req.headers);
        if (!req
            || !req.body
            || !req.body.hasOwnProperty('listStock')) {
          return resolvePr({
            data: [],
            messageErr: 'Call error param not valid',
            status: 'FAILED',
          });
        }
        const { listStock } = req.body;

        const data = JSON.stringify({ listStock });

        const configCall = {
          data,
          method: 'post',
          timeout: 1000*30, 
          maxBodyLength: Infinity,
          url: `${stockMarketUrl}/stock-market`,
          headers: {
            'Content-Type': 'application/json',
            'access-token-authen': req.headers['access-token-authen'],
            'lang': req.headers['lang'] || 'VI',
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              // logger.info(JSON.stringify(response.data));
              // console.log({ response: response.data })

              const { status, message } = response.data;

              return resolvePr({ status, data: response.data, messageErr: message });
            })
            .catch((error) => {
              logger.info(error);
              return resolvePr({ data: [], messageErr: JSON.stringify(error) });
            });

        return;
      });
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }
}

const insStockMarketService = new StockMarketService();

export default insStockMarketService;
