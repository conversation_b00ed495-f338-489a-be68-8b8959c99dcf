import { MongoDBClient } from '../database/mongodb';
import mongoDBService from './MongoDBService';
import logger from '../config/logger';

/**
 * Database Connection Service
 * Tích hợp MongoDBClient với cấu hình environment và cung cấp interface thống nhất
 */
export class DatabaseConnectionService {
    private static instance: DatabaseConnectionService;
    private isConnected: boolean = false;
    private connectionConfig: {
        uri: string;
        dbName: string;
    } | null = null;

    private constructor() {}

    public static getInstance(): DatabaseConnectionService {
        if (!DatabaseConnectionService.instance) {
            DatabaseConnectionService.instance = new DatabaseConnectionService();
        }
        return DatabaseConnectionService.instance;
    }

    /**
     * Kết nối đến MongoDB sử dụng cấu hình từ environment
     */
    async connect(): Promise<void> {
        try {
            if (this.isConnected) {
                logger.info('📦 MongoDB already connected');
                return;
            }

            // L<PERSON>y cấu hình từ environment
            const uri = process.env.MONGODB_URI || 'mongodb://localhost:27017/fos_ekyc_dev';
            const dbName = process.env.MONGODB_DB_NAME || 'fos_ekyc_dev';

            logger.info(`🔌 Connecting to MongoDB: ${uri}`);
            
            // Kết nối sử dụng MongoDBClient
            await MongoDBClient.connect(uri, dbName);
            
            this.connectionConfig = { uri, dbName };
            this.isConnected = true;
            
            logger.info('✅ MongoDB connected successfully');
        } catch (error) {
            logger.error('❌ Failed to connect to MongoDB:', error);
            throw error;
        }
    }

    /**
     * Ngắt kết nối MongoDB
     */
    async disconnect(): Promise<void> {
        try {
            if (!this.isConnected) {
                logger.info('📦 MongoDB already disconnected');
                return;
            }

            await MongoDBClient.disconnect();
            this.isConnected = false;
            this.connectionConfig = null;
            
            logger.info('✅ MongoDB disconnected successfully');
        } catch (error) {
            logger.error('❌ Failed to disconnect from MongoDB:', error);
            throw error;
        }
    }

    /**
     * Kiểm tra trạng thái kết nối
     */
    getConnectionStatus(): boolean {
        return this.isConnected;
    }

    /**
     * Lấy thông tin cấu hình kết nối
     */
    getConnectionConfig() {
        return this.connectionConfig;
    }

    /**
     * Health check cho database
     */
    async healthCheck(): Promise<{
        status: string;
        details: any;
    }> {
        try {
            if (!this.isConnected) {
                return {
                    status: 'disconnected',
                    details: {
                        message: 'Database not connected',
                        config: this.connectionConfig
                    }
                };
            }

            // Thử thực hiện một query đơn giản để kiểm tra kết nối
            const db = MongoDBClient.getDb();
            const adminDb = db.admin();
            const result = await adminDb.ping();

            return {
                status: 'connected',
                details: {
                    ping: result,
                    config: this.connectionConfig,
                    timestamp: new Date().toISOString()
                }
            };
        } catch (error) {
            return {
                status: 'error',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    config: this.connectionConfig
                }
            };
        }
    }

    /**
     * Lấy database statistics
     */
    async getDatabaseStats(): Promise<any> {
        try {
            if (!this.isConnected) {
                throw new Error('Database not connected');
            }

            const db = MongoDBClient.getDb();
            const stats = await db.stats();
            return stats;
        } catch (error) {
            logger.error('❌ Error getting database stats:', error);
            throw error;
        }
    }

    /**
     * Lấy danh sách collections
     */
    async listCollections(): Promise<string[]> {
        try {
            if (!this.isConnected) {
                throw new Error('Database not connected');
            }

            const db = MongoDBClient.getDb();
            const collections = await db.listCollections().toArray();
            return collections.map(col => col.name);
        } catch (error) {
            logger.error('❌ Error listing collections:', error);
            throw error;
        }
    }

    /**
     * Tạo collection với options
     */
    async createCollection(name: string, options?: any): Promise<void> {
        try {
            if (!this.isConnected) {
                throw new Error('Database not connected');
            }

            const db = MongoDBClient.getDb();
            await db.createCollection(name, options);
            logger.info(`✅ Collection created: ${name}`);
        } catch (error) {
            logger.error(`❌ Error creating collection ${name}:`, error);
            throw error;
        }
    }

    /**
     * CRUD Operations - Wrapper cho MongoDBService
     */

    // INSERT
    async insert<T>(collectionName: string, document: T) {
        return await mongoDBService.insertOne(collectionName, document as any);
    }

    async insertMany<T>(collectionName: string, documents: T[]) {
        return await mongoDBService.insertMany(collectionName, documents as any);
    }

    // SELECT
    async findOne<T>(collectionName: string, filter: any, options?: any) {
        return await mongoDBService.findOne<T>(collectionName, filter, options);
    }

    async findMany<T>(collectionName: string, filter: any = {}, options?: any) {
        return await mongoDBService.findMany<T>(collectionName, filter, options);
    }

    async findWithPagination<T>(
        collectionName: string,
        filter: any = {},
        page: number = 1,
        limit: number = 10,
        sort?: any
    ) {
        return await mongoDBService.findWithPagination<T>(collectionName, filter, page, limit, sort);
    }

    async count(collectionName: string, filter: any = {}) {
        return await mongoDBService.count(collectionName, filter);
    }

    // UPDATE
    async updateOne<T>(collectionName: string, filter: any, update: any) {
        return await mongoDBService.updateOne<T>(collectionName, filter, update);
    }

    async updateMany<T>(collectionName: string, filter: any, update: any) {
        return await mongoDBService.updateMany<T>(collectionName, filter, update);
    }

    async upsert<T>(collectionName: string, filter: any, update: any) {
        return await mongoDBService.upsert<T>(collectionName, filter, update);
    }

    // DELETE
    async deleteOne(collectionName: string, filter: any) {
        return await mongoDBService.deleteOne(collectionName, filter);
    }

    async deleteMany(collectionName: string, filter: any) {
        return await mongoDBService.deleteMany(collectionName, filter);
    }

    // UTILITY
    async aggregate<T>(collectionName: string, pipeline: any[]) {
        return await mongoDBService.aggregate<T>(collectionName, pipeline);
    }

    async distinct(collectionName: string, field: string, filter: any = {}) {
        return await mongoDBService.distinct(collectionName, field, filter);
    }

    async createIndex(collectionName: string, indexSpec: any, options?: any) {
        return await mongoDBService.createIndex(collectionName, indexSpec, options);
    }

    async collectionExists(collectionName: string) {
        return await mongoDBService.collectionExists(collectionName);
    }

    async dropCollection(collectionName: string) {
        return await mongoDBService.dropCollection(collectionName);
    }

    /**
     * Transaction support
     */
    async withTransaction<T>(operation: () => Promise<T>): Promise<T> {
        const db = MongoDBClient.getDb();
        const client = (db as any).client;
        const session = client.startSession();
        
        try {
            session.startTransaction();
            const result = await operation();
            await session.commitTransaction();
            return result;
        } catch (error) {
            await session.abortTransaction();
            throw error;
        } finally {
            await session.endSession();
        }
    }
}

// Export singleton instance
const databaseConnectionService = DatabaseConnectionService.getInstance();
export default databaseConnectionService;
