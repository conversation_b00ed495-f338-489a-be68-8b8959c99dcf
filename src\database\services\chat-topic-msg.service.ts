import logger from '../../config/logger';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopic, ChatCusTopicMsg, COLLECTION_CHAT_CUS_TOPIC, COLLECTION_CHAT_CUS_TOPIC_MSG } from '../dto/ChatCusTopic';
import {ObjectId} from 'mongodb';

interface FilterChatMsg {
    filter: object,
    page: number,
    limit: number,
    sort?: string,
    sortOrder?: 'asc' | 'desc'
}

const checkValid = (input: string) => {
    if (input == '' || input == undefined || input == null) {
        return false;
    }
    return true;
}

// Tạo message mới
const createMessage = async (data: ChatCusTopicMsg) => {
    try {
        if (!checkValid(data.topic_id) || !checkValid(data.message) || !checkValid(data.sender)) {
            logger.error('Invalid input data for ChatCusTopicMsg');
            return { success: false, message: 'Invalid input data' };
        }

        data.created_at = new Date();
        data.updated_at = new Date();

        const result = await databaseConnectionService.insert<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            data
        );
        logger.info(`ChatCusTopicMsg created with ID: ${result.insertedId}`);
        return { success: result.insertedId != null};
    } catch (error) {
        logger.error('Error creating ChatCusTopicMsg:', error);
        throw error;
    }
}

// Cập nhật message
const updateMessage = async (messageId: string, data: Partial<ChatCusTopicMsg>) => {
    try {
        if (!checkValid(messageId) || !checkValid(data.topic_id) || !checkValid(data.fos_id)) {
            logger.error('Invalid message ID');
            return { success: false, message: 'Invalid message ID' };
        }

        // kiểm tra dữ liệu msg_id thuộc fos_id và topic_id
        const message = await databaseConnectionService.findOne<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId), topic_id: data.topic_id, fos_id: data.fos_id }
        );
        console.log('message: ', message);

        if (!message) {
            logger.error('Message not found');
            return { success: false, message: 'Message not found' };
        }

        data.updated_at = new Date();

        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId) },
            { $set: data }
        );
        logger.info(`ChatCusTopicMsg updated for ID: ${messageId}`);
        return { success: result.modifiedCount == 1 };

    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg:', error);
        throw error;
    }
}

// Xóa message
const deleteMessage = async (messageId: string, fos_id: string,  topic_id:string ) => {
    try {
        if (!checkValid(messageId) || !checkValid(fos_id) || !checkValid(topic_id)) {
            logger.error('Invalid input params');
            return { success: false, message: 'Invalid input params' };
        }
        // kiểm tra dữ liệu msg_id thuộc fos_id và topic_id
        const message = await databaseConnectionService.findOne<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId), topic_id: topic_id, fos_id: fos_id }
        );
        if (!message) {
            logger.error('Message not found');
            return { success: false, message: 'Message not found' };
        }

        const result = await databaseConnectionService.deleteOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId) }
        );
        logger.info(`ChatCusTopicMsg deleted for ID: ${messageId}`);
        return { success: result.deletedCount == 1 };

    } catch (error) {
        logger.error('Error deleting ChatCusTopicMsg:', error);
        throw error;
    }
}

// Lấy danh sách messages với phân trang cải tiến
const getMessages = async (data: FilterChatMsg) => {
    try {
        const page = data.page ?? 1;
        const limit = data.limit ?? 10;
        const sort = data.sort ?? 'created_at';
        const sortOrder = data.sortOrder ?? 'desc';

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            data.filter,
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`ChatCusTopicMsg retrieved with filter: ${JSON.stringify(data.filter)}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopicMsg:', error);
        throw error;
    }
}


// Lấy messages theo topic_id với phân trang cải tiến
const getMessagesByTopicId = async (
    fos_id: string,
    topicId: string,
    page: number = 1,
    limit: number = 10,
    sort: string = 'created_at',
    sortOrder: 'asc' | 'desc' = 'desc'
) => {
    try {
        if (!checkValid(topicId) || !checkValid(fos_id)) {
            logger.error('Invalid input params');
            return { success: false, message: 'Invalid input params' };
        }
        // Kiểm tra topic_id có thuộc fos_id không
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: new ObjectId(topicId), fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { topic_id: topicId },
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`ChatCusTopicMsg retrieved for topic_id: ${topicId}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopicMsg by topic ID:', error);
        throw error;
    }
}

// Cập nhật reaction cho message
const updateMessageReaction = async (fos_id:string, topic_id:string, messageId: string, reaction: number) => {
    try {
        if (!checkValid(messageId) || !checkValid(fos_id) || !checkValid(topic_id)|| !checkValid(reaction.toString())) {
            logger.error('Invalid input params');
            return { success: false, message: 'Invalid input params' };
        }
        // kiểm tra topic_id có thuộc fos_id
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: new ObjectId(topic_id), fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }
        
        // kiểm tra messageId exist với fos_id và topic_id
        const messageExist = await databaseConnectionService.findOne<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId), fos_id: fos_id, topic_id: topic_id }
        );
        if (!messageExist) {
            logger.error('Message not found');
            return { success: false, message: 'Message not found' };
        }

        // kiểm tra messageId exist
        const message = await databaseConnectionService.findOne<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(topic_id) }
        );
        if (!message) {
            logger.error('Message not found');
            return { success: false, message: 'Message not found' };
        }


        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: new ObjectId(messageId) },
            { $set: { reaction: reaction, updated_at: new Date() } }
        );
        logger.info(`ChatCusTopicMsg reaction updated for ID: ${messageId}`);
        return { success: result.modifiedCount == 1 };

    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg reaction:', error);
        throw error;
    }
}

export default {
    createMessage,
    updateMessage,
    deleteMessage,
    getMessages,
    // getMessageById,
    getMessagesByTopicId,
    // deleteManyMessages,
    updateMessageReaction,
}
