import logger from '../../config/logger';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopicMsg, COLLECTION_CHAT_CUS_TOPIC_MSG } from '../dto/ChatCusTopic';

interface FilterChatMsg {
    filter: object,
    page: number,
    limit: number,
}

const checkValid = (input: string) => {
    if (input == '' || input == undefined || input == null) {
        return false;
    }
    return true;
}

// Tạo message mới
const createMessage = async (data: ChatCusTopicMsg) => {
    try {
        if (!checkValid(data.topic_id) || !checkValid(data.message) || !checkValid(data.sender)) {
            logger.error('Invalid input data for ChatCusTopicMsg');
            return { success: false, message: 'Invalid input data' };
        }

        data.created_at = new Date();
        data.updated_at = new Date();

        const result = await databaseConnectionService.insert<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            data
        );
        logger.info(`ChatCusTopicMsg created with ID: ${result.insertedId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error creating ChatCusTopicMsg:', error);
        throw error;
    }
}

// Cập nhật message
const updateMessage = async (messageId: string, data: Partial<ChatCusTopicMsg>) => {
    try {
        if (!checkValid(messageId)) {
            logger.error('Invalid message ID');
            return { success: false, message: 'Invalid message ID' };
        }

        data.updated_at = new Date();

        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: messageId },
            { $set: data }
        );
        logger.info(`ChatCusTopicMsg updated for ID: ${messageId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg:', error);
        throw error;
    }
}

// Xóa message
const deleteMessage = async (messageId: string) => {
    try {
        if (!checkValid(messageId)) {
            logger.error('Invalid message ID');
            return { success: false, message: 'Invalid message ID' };
        }

        const result = await databaseConnectionService.deleteOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: messageId }
        );
        logger.info(`ChatCusTopicMsg deleted for ID: ${messageId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error deleting ChatCusTopicMsg:', error);
        throw error;
    }
}

// Lấy danh sách messages với phân trang
const getMessages = async (data: FilterChatMsg) => {
    try {
        const result = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            data.filter,
            data.page ?? 1,
            data.limit ?? 10
        );
        logger.info(`ChatCusTopicMsg retrieved with filter: ${JSON.stringify(data.filter)}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopicMsg:', error);
        throw error;
    }
}

// Lấy message theo ID
const getMessageById = async (messageId: string) => {
    try {
        if (!checkValid(messageId)) {
            logger.error('Invalid message ID');
            return { success: false, message: 'Invalid message ID' };
        }

        const result = await databaseConnectionService.findOne<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: messageId }
        );
        logger.info(`ChatCusTopicMsg retrieved for ID: ${messageId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopicMsg by ID:', error);
        throw error;
    }
}

// Lấy messages theo topic_id
const getMessagesByTopicId = async (topicId: string, page: number = 1, limit: number = 10) => {
    try {
        if (!checkValid(topicId)) {
            logger.error('Invalid topic ID');
            return { success: false, message: 'Invalid topic ID' };
        }

        const result = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { topic_id: topicId },
            page,
            limit
        );
        logger.info(`ChatCusTopicMsg retrieved for topic_id: ${topicId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopicMsg by topic ID:', error);
        throw error;
    }
}

// Xóa nhiều messages theo điều kiện
const deleteManyMessages = async (filter: object) => {
    try {
        const result = await databaseConnectionService.deleteMany(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            filter
        );
        logger.info(`ChatCusTopicMsg deleted many with filter: ${JSON.stringify(filter)}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error deleting many ChatCusTopicMsg:', error);
        throw error;
    }
}

// Cập nhật reaction cho message
const updateMessageReaction = async (messageId: string, reaction: number) => {
    try {
        if (!checkValid(messageId)) {
            logger.error('Invalid message ID');
            return { success: false, message: 'Invalid message ID' };
        }

        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { _id: messageId },
            { $set: { reaction: reaction, updated_at: new Date() } }
        );
        logger.info(`ChatCusTopicMsg reaction updated for ID: ${messageId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg reaction:', error);
        throw error;
    }
}

export default {
    createMessage,
    updateMessage,
    deleteMessage,
    getMessages,
    getMessageById,
    getMessagesByTopicId,
    deleteManyMessages,
    updateMessageReaction,
}
