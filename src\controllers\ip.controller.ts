import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import insIpService from '../services/IpService';

const getIp: IController = async (req, res) => {
  try {
    const result = await insIpService.getIpAddress(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );

  }
  return apiResponse.result(res, { response: 'OK' });
}

const CheckHealthy: IController = async (req, res) => {
  try {
    const result = await insIpService.getIpAddress(req);
    apiResponse.result(res, {
      ...result,
      server: "FOS-EKYC"
    });
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );

  }
  return apiResponse.result(res, { response: 'OK' });
}

export default {
  getIp,
  CheckHealthy
}
