// Example usage của Chat Topic APIs
// File này chỉ để tham khảo, không chạy trực tiếp

import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api';

// ============ ChatCusTopic Examples ============

// 1. Tạo chat topic mới
export const createChatTopicExample = async () => {
    try {
        const response = await axios.post(`${API_BASE_URL}/chat-topics`, {
            fos_id: "FOS001",
            topic_id: "TOPIC001",
            topic_name: "Hỗ trợ khách hàng"
        });
        
        console.log('Chat topic created:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error creating chat topic:', error.response?.data);
        throw error;
    }
};

// 2. Cập nhật chat topic
export const updateChatTopicExample = async () => {
    try {
        const response = await axios.put(`${API_BASE_URL}/chat-topics/FOS001/TOPIC001`, {
            topic_name: "Hỗ trợ khách hàng VIP"
        });
        
        console.log('Chat topic updated:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error updating chat topic:', error.response?.data);
        throw error;
    }
};

// 3. Lấy chat topic theo ID
export const getChatTopicByIdExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics/FOS001/TOPIC001`);
        
        console.log('Chat topic retrieved:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error getting chat topic:', error.response?.data);
        throw error;
    }
};

// 4. Lấy danh sách chat topics với filter
export const getChatTopicsExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics`, {
            params: {
                page: 1,
                limit: 10,
                fos_id: "FOS001"
            }
        });
        
        console.log('Chat topics list:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error getting chat topics:', error.response?.data);
        throw error;
    }
};

// 5. Lấy chat topics theo fos_id
export const getChatTopicsByFosIdExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/chat-topics/fos/FOS001`, {
            params: {
                page: 1,
                limit: 5
            }
        });
        
        console.log('Chat topics by FOS ID:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error getting chat topics by FOS ID:', error.response?.data);
        throw error;
    }
};

// 6. Xóa chat topic
export const deleteChatTopicExample = async () => {
    try {
        const response = await axios.delete(`${API_BASE_URL}/chat-topics/FOS001/TOPIC001`);
        
        console.log('Chat topic deleted:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error deleting chat topic:', error.response?.data);
        throw error;
    }
};

// ============ ChatCusTopicMsg Examples ============

// 1. Tạo message mới
export const createMessageExample = async () => {
    try {
        const response = await axios.post(`${API_BASE_URL}/messages`, {
            topic_id: "TOPIC001",
            message: "Xin chào, tôi cần hỗ trợ về sản phẩm",
            sender: "customer001",
            reciever: "support001",
            reaction: 0
        });
        
        console.log('Message created:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error creating message:', error.response?.data);
        throw error;
    }
};

// 2. Cập nhật message
export const updateMessageExample = async () => {
    try {
        const messageId = "MESSAGE_ID_HERE";
        const response = await axios.put(`${API_BASE_URL}/messages/${messageId}`, {
            message: "Xin chào, tôi cần hỗ trợ về sản phẩm ABC",
            reaction: 1
        });
        
        console.log('Message updated:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error updating message:', error.response?.data);
        throw error;
    }
};

// 3. Lấy messages theo topic_id
export const getMessagesByTopicIdExample = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/messages/topic/TOPIC001`, {
            params: {
                page: 1,
                limit: 20
            }
        });
        
        console.log('Messages by topic ID:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error getting messages:', error.response?.data);
        throw error;
    }
};

// 4. Cập nhật reaction cho message
export const updateMessageReactionExample = async () => {
    try {
        const messageId = "MESSAGE_ID_HERE";
        const response = await axios.patch(`${API_BASE_URL}/messages/${messageId}/reaction`, {
            reaction: 5
        });
        
        console.log('Message reaction updated:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error updating message reaction:', error.response?.data);
        throw error;
    }
};

// 5. Xóa message
export const deleteMessageExample = async () => {
    try {
        const messageId = "MESSAGE_ID_HERE";
        const response = await axios.delete(`${API_BASE_URL}/messages/${messageId}`);
        
        console.log('Message deleted:', response.data);
        return response.data;
    } catch (error) {
        console.error('Error deleting message:', error.response?.data);
        throw error;
    }
};

// ============ Complete Workflow Example ============

export const completeWorkflowExample = async () => {
    try {
        console.log('=== Starting complete workflow ===');
        
        // 1. Tạo chat topic
        const topicResult = await createChatTopicExample();
        console.log('✓ Chat topic created');
        
        // 2. Tạo một số messages
        const message1 = await createMessageExample();
        console.log('✓ First message created');
        
        // 3. Lấy messages theo topic
        const messages = await getMessagesByTopicIdExample();
        console.log('✓ Messages retrieved');
        
        // 4. Cập nhật reaction
        if (messages.data.items.length > 0) {
            const firstMessageId = messages.data.items[0]._id;
            // Note: Trong thực tế, bạn sẽ sử dụng messageId thực
            console.log('✓ Would update reaction for message:', firstMessageId);
        }
        
        // 5. Lấy chat topics
        const topics = await getChatTopicsExample();
        console.log('✓ Chat topics list retrieved');
        
        console.log('=== Workflow completed successfully ===');
        
    } catch (error) {
        console.error('Workflow failed:', error);
    }
};

// Error handling example
export const errorHandlingExample = async () => {
    try {
        // Thử tạo chat topic với dữ liệu không hợp lệ
        await axios.post(`${API_BASE_URL}/chat-topics`, {
            fos_id: "", // Invalid: empty string
            topic_name: "Test"
            // Missing topic_id
        });
    } catch (error) {
        if (error.response?.status === 400) {
            console.log('Validation errors:', error.response.data.errors);
            // Expected output:
            // [
            //   "fos_id is required and must be a valid string",
            //   "topic_id is required and must be a valid string"
            // ]
        }
    }
};
