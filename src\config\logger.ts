import winston from 'winston';
import moment from 'moment';

const { combine, timestamp, json, label, printf } = winston.format;

const myFormat = printf(({ level, message, label, timestamp }) => {
    return `${timestamp} [${label}] ${level}: ${message}`;
  });
const logger = winston.createLogger({
    level: 'info',
    format: combine(
        label({ label: 'FOS-EKYC' }),
        timestamp({
            format: () => moment().format('DD-MM-YYYY HH:mm:ss:SSS'),
        }),
        myFormat,
    ),
    transports: [
        //
        // - Write to all logs with level `info` and below to `combined.log`
        // - Write all logs error (and below) to `error.log`.
        //
        new winston.transports.File({
            filename: `./log/error_${moment().format('DDMMYYYY')}.log`,
            level: 'error',
        }),
        new winston.transports.File({
            filename: `./log/info_${moment().format('DDMMYYYY')}.log`,
        }),
    ],
});

//
// If we're not in production then log to the `console` with the format:
// `${info.level}: ${info.message} JSON.stringify({ ...rest }) `
//
if (process.env.NODE_ENV !== 'production') {
    logger.add(
        new winston.transports.Console({
            // format: winston.format.json(),
            format: combine(
                label({ label: 'FOS-EKYC' }),
                timestamp({
                    format: () => moment().format('DD-MM-YYYY HH:mm:ss:SSS'),
                }),
                myFormat,
            ),
        }),
    );
}

export default logger;
