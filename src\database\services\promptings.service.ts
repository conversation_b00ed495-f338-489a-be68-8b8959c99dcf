import logger from '../../config/logger';
import { ObjectId } from 'mongodb';
import databaseConnectionService from '../DatabaseConnectionService';
import { PromptingsModel, COLLECTION_SSV_DIC } from '../dto/PromptingsModel';

interface FilterPromptings {
    filter: object;
    page: number;
    limit: number;
    sort?: string;
    sortOrder?: 'asc' | 'desc';
}

const checkValid = (input: string) => {
    if (input == '' || input == undefined || input == null) {
        return false;
    }
    return true;
}

// Tạo prompting mới
const createPrompting = async (data: PromptingsModel) => {
    try {
        if (!checkValid(data.LNG_TP) || !checkValid(data.COL_CD) || !checkValid(data.COL_CD_TP)) {
            logger.error('Invalid input data for PromptingsModel');
            return { success: false, message: 'Invalid input data: LNG_TP, COL_CD, and COL_CD_TP are required' };
        }

        // Validate ACTIVE_YN (should be 0 or 1)
        if (data.ACTIVE_YN !== 0 && data.ACTIVE_YN !== 1) {
            logger.error('Invalid ACTIVE_YN value');
            return { success: false, message: 'ACTIVE_YN must be 0 or 1' };
        }

        const result = await databaseConnectionService.insert<PromptingsModel>(
            COLLECTION_SSV_DIC,
            data
        );
        
        logger.info(`PromptingsModel created with ID: ${result.insertedId}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error creating PromptingsModel:', error);
        throw error;
    }
}

// Cập nhật prompting
const updatePrompting = async (id: string, data: Partial<PromptingsModel>) => {
    try {
        if (!checkValid(id)) {
            logger.error('Invalid prompting ID');
            return { success: false, message: 'Invalid prompting ID' };
        }

        // Validate ACTIVE_YN if provided
        if (data.ACTIVE_YN !== undefined && data.ACTIVE_YN !== 0 && data.ACTIVE_YN !== 1) {
            logger.error('Invalid ACTIVE_YN value');
            return { success: false, message: 'ACTIVE_YN must be 0 or 1' };
        }

        const result = await databaseConnectionService.updateOne(
            COLLECTION_SSV_DIC,
            { _id: new ObjectId(id) },
            { $set: data }
        );
        
        if (result.matchedCount === 0) {
            return { success: false, message: 'Prompting not found' };
        }
        
        logger.info(`PromptingsModel updated for ID: ${id}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error updating PromptingsModel:', error);
        throw error;
    }
}

// Xóa prompting
const deletePrompting = async (id: string) => {
    try {
        if (!checkValid(id)) {
            logger.error('Invalid prompting ID');
            return { success: false, message: 'Invalid prompting ID' };
        }

        const result = await databaseConnectionService.deleteOne(
            COLLECTION_SSV_DIC,
            { _id: new ObjectId(id) }
        );
        
        if (result.deletedCount === 0) {
            return { success: false, message: 'Prompting not found' };
        }
        
        logger.info(`PromptingsModel deleted for ID: ${id}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error deleting PromptingsModel:', error);
        throw error;
    }
}

// Lấy danh sách promptings với phân trang
const getPromptings = async (data: FilterPromptings) => {
    try {
        const page = data.page ?? 1;
        const limit = data.limit ?? 10;
        const sort = data.sort ?? '_id';
        const sortOrder = data.sortOrder ?? 'desc';

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_SSV_DIC,
            data.filter,
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`PromptingsModel retrieved with filter: ${JSON.stringify(data.filter)}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving PromptingsModel:', error);
        throw error;
    }
}

// Lấy prompting theo ID
const getPromptingById = async (id: string) => {
    try {
        if (!checkValid(id)) {
            logger.error('Invalid prompting ID');
            return { success: false, message: 'Invalid prompting ID' };
        }

        const result = await databaseConnectionService.findOne<PromptingsModel>(
            COLLECTION_SSV_DIC,
            { _id: new ObjectId(id) }
        );
        
        if (!result) {
            return { success: false, message: 'Prompting not found' };
        }
        
        logger.info(`PromptingsModel retrieved for ID: ${id}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error retrieving PromptingsModel by ID:', error);
        throw error;
    }
}

// Lấy promptings theo LNG_TP
const getPromptingsByLanguage = async (
    lng_tp: string, 
    page: number = 1, 
    limit: number = 10, 
    sort: string = '_id', 
    sortOrder: 'asc' | 'desc' = 'desc'
) => {
    try {
        if (!checkValid(lng_tp)) {
            logger.error('Invalid LNG_TP');
            return { success: false, message: 'Invalid LNG_TP' };
        }

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_SSV_DIC,
            { LNG_TP: lng_tp },
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`PromptingsModel retrieved for LNG_TP: ${lng_tp}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving PromptingsModel by LNG_TP:', error);
        throw error;
    }
}

// Lấy promptings theo COL_CD_TP
const getPromptingsByCodeType = async (
    col_cd_tp: string, 
    page: number = 1, 
    limit: number = 10, 
    sort: string = '_id', 
    sortOrder: 'asc' | 'desc' = 'desc'
) => {
    try {
        if (!checkValid(col_cd_tp)) {
            logger.error('Invalid COL_CD_TP');
            return { success: false, message: 'Invalid COL_CD_TP' };
        }

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_SSV_DIC,
            { COL_CD_TP: col_cd_tp },
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`PromptingsModel retrieved for COL_CD_TP: ${col_cd_tp}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving PromptingsModel by COL_CD_TP:', error);
        throw error;
    }
}

// Lấy promptings active/inactive
const getPromptingsByStatus = async (
    active_yn: number, 
    page: number = 1, 
    limit: number = 10, 
    sort: string = '_id', 
    sortOrder: 'asc' | 'desc' = 'desc'
) => {
    try {
        if (active_yn !== 0 && active_yn !== 1) {
            logger.error('Invalid ACTIVE_YN value');
            return { success: false, message: 'ACTIVE_YN must be 0 or 1' };
        }

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_SSV_DIC,
            { ACTIVE_YN: active_yn },
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`PromptingsModel retrieved for ACTIVE_YN: ${active_yn}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving PromptingsModel by ACTIVE_YN:', error);
        throw error;
    }
}

// Xóa nhiều promptings theo điều kiện
const deleteManyPromptings = async (filter: object) => {
    try {
        const result = await databaseConnectionService.deleteMany(
            COLLECTION_SSV_DIC,
            filter
        );
        logger.info(`PromptingsModel deleted many with filter: ${JSON.stringify(filter)}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error deleting many PromptingsModel:', error);
        throw error;
    }
}

// Cập nhật nhiều promptings theo điều kiện
const updateManyPromptings = async (filter: object, updateData: Partial<PromptingsModel>) => {
    try {
        // Validate ACTIVE_YN if provided
        if (updateData.ACTIVE_YN !== undefined && updateData.ACTIVE_YN !== 0 && updateData.ACTIVE_YN !== 1) {
            logger.error('Invalid ACTIVE_YN value');
            return { success: false, message: 'ACTIVE_YN must be 0 or 1' };
        }

        const result = await databaseConnectionService.updateMany(
            COLLECTION_SSV_DIC,
            filter,
            { $set: updateData }
        );
        logger.info(`PromptingsModel updated many with filter: ${JSON.stringify(filter)}`);
        return { success: true, data: result };

    } catch (error) {
        logger.error('Error updating many PromptingsModel:', error);
        throw error;
    }
}

export default {
    createPrompting,
    updatePrompting,
    deletePrompting,
    getPromptings,
    getPromptingById,
    getPromptingsByLanguage,
    getPromptingsByCodeType,
    getPromptingsByStatus,
    deleteManyPromptings,
    updateManyPromptings,
};
