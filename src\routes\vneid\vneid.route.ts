import express from 'express';
import vneidController from '../../controllers/vneid.controller';
const router = express.Router();

router.post(
    '/upload-image',
    vneidController.uploadImage
);

router.get(
    '/download-image',
    vneidController.downloadImage
);

router.post(
    '/fetch-access-token',
    vneidController.loginGetAccessToken
);

router.post(
    '/read-nfc',
    vneidController.readNFC
);

router.post(
    '/verify-bca',
    vneidController.verifyNFC
);
export default router;
