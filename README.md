# 3-FOS-EKYC System

## 📋 Tổng quan hệ thống

**3-FOS-EKYC** là một hệ thống tích hợp đa chức năng phục vụ cho các dịch vụ tà<PERSON>, bao <PERSON>ồ<PERSON> e<PERSON> (electronic Know Your Customer), qu<PERSON><PERSON> lý hợp đồng điện tử, chat system, và nhiều tính năng khác.

### 🏗️ Kiến trúc tổng thể

```
3-fos-ekyc/
├── src/
│   ├── config/           # Cấu hình hệ thống
│   ├── constants/        # Hằng số và constants
│   ├── controllers/      # API Controllers
│   ├── database/         # Database layer
│   ├── docs/            # API Documentation
│   ├── examples/        # Usage examples
│   ├── middleware/      # Express middlewares
│   ├── routes/          # API routes
│   ├── services/        # Business logic services
│   └── share/           # Shared utilities
├── env/                 # Environment configurations
└── README.md
```

### 🚀 Công nghệ sử dụng

- **Runtime**: Node.js + TypeScript
- **Framework**: Express.js
- **Database**: MongoDB
- **Authentication**: JWT
- **Logging**: Winston
- **Validation**: Joi
- **Documentation**: Markdown + JSDoc

---

## 🔧 Cấu hình và khởi chạy

### Environment Setup

Hệ thống hỗ trợ 2 môi trường:
- **Development**: `ENV=dev`
- **Production**: `ENV=pro`

```bash
# Development
ENV=dev npm start

# Production
ENV=pro npm start
```

### Cấu trúc Environment Files

```
env/
├── dev/
│   └── .env          # Development config
└── pro/
    └── .env          # Production config
```

### Build và Release

```bash
# Kiểm tra thư mục env
yarn build

# Install dependencies
npm install

# Start development server
npm run dev

# Start production server
npm start
```

**Default Port**: 8500

---

## 📊 Kiến trúc Database

### Database Connection Service

**File**: `src/database/DatabaseConnectionService.ts`

Quản lý kết nối MongoDB với các tính năng:
- Connection pooling
- Auto-reconnection
- Error handling
- Query optimization

### Data Models (DTOs)

#### 1. ChatCusTopic
**File**: `src/database/dto/ChatCusTopic.ts`
```typescript
interface ChatCusTopic {
  _id?: string;
  fos_id: string;
  topic_id: string;
  topic_name: string;
  created_at: Date;
  updated_at: Date;
}

interface ChatCusTopicMsg {
  _id?: string;
  topic_id: string;
  message: string;
  sender: string;
  reciever: string;
  reaction: number;
  created_at: Date;
  updated_at: Date;
}
```

#### 2. PromptingsModel
**File**: `src/database/dto/PromptingsModel.ts`
```typescript
interface PromptingsModel {
  _id?: string;
  LNG_TP: string;         // Language Type
  COL_CD: string;         // Column Code
  COL_CD_TP: string;      // Column Code Type
  COL_CD_TP_NM: string;   // Column Code Type Name
  ACTIVE_YN: number;      // Active Status (0|1)
}
```

#### 3. OCRModel
**File**: `src/database/dto/OCRModel.ts`
- Quản lý dữ liệu OCR (Optical Character Recognition)
- Xử lý nhận dạng văn bản từ hình ảnh

---

## 🎯 Chức năng chính

### 1. 💬 Chat System

**Mô tả**: Hệ thống chat đa chức năng với quản lý topics và messages

**Components**:
- **Service**: `src/database/services/chat-history.service.ts`
- **Service**: `src/database/services/chat-topic-msg.service.ts`
- **Controller**: `src/controllers/chat-topic.controller.ts`
- **Routes**: `src/routes/history-chat/chat-topic.routes.ts`
- **Validation**: `src/middleware/chat-topic.validation.ts`

**Tính năng**:
- ✅ CRUD operations cho chat topics
- ✅ CRUD operations cho messages
- ✅ Pagination và sorting
- ✅ Filter theo FOS ID
- ✅ Reaction system
- ✅ Real-time messaging support

**API Endpoints**:
```
POST   /api/chat/chat-topics              # Tạo topic mới
GET    /api/chat/chat-topics              # Lấy danh sách topics
GET    /api/chat/chat-topics/:fos_id/:topic_id  # Lấy topic theo ID
PUT    /api/chat/chat-topics/:fos_id/:topic_id  # Cập nhật topic
DELETE /api/chat/chat-topics/:fos_id/:topic_id  # Xóa topic

POST   /api/chat/messages                 # Tạo message mới
GET    /api/chat/messages/topic/:topicId  # Lấy messages theo topic
PUT    /api/chat/messages/:messageId      # Cập nhật message
DELETE /api/chat/messages/:messageId      # Xóa message
PUT    /api/chat/messages/:messageId/reaction  # Cập nhật reaction
```

**Usage Example**:
```typescript
// Tạo chat topic
const topic = await axios.post('/api/chat/chat-topics', {
  fos_id: 'FOS001',
  topic_id: 'TOPIC001',
  topic_name: 'Customer Support'
});

// Gửi message
const message = await axios.post('/api/chat/messages', {
  topic_id: 'TOPIC001',
  message: 'Hello, I need help',
  sender: 'customer001',
  reciever: 'support001'
});
```

### 2. 📝 Promptings System (Dictionary Management)

**Mô tả**: Hệ thống quản lý từ điển đa ngôn ngữ cho ứng dụng

**Components**:
- **Service**: `src/database/services/promptings.service.ts`
- **Controller**: `src/controllers/promptings.controller.ts`
- **Routes**: `src/routes/promptings.routes.ts`
- **Validation**: `src/middleware/promptings.validation.ts`

**Tính năng**:
- ✅ CRUD operations cho dictionary entries
- ✅ Multi-language support
- ✅ Code mapping system
- ✅ Active/Inactive status management
- ✅ Bulk operations
- ✅ Advanced filtering và search

**API Endpoints**:
```
POST   /api/promptings                    # Tạo entry mới
GET    /api/promptings                    # Lấy danh sách entries
GET    /api/promptings/:id                # Lấy entry theo ID
PUT    /api/promptings/:id                # Cập nhật entry
DELETE /api/promptings/:id                # Xóa entry

GET    /api/promptings/language/:lng_tp   # Lấy theo ngôn ngữ
GET    /api/promptings/code-type/:col_cd_tp  # Lấy theo code type
GET    /api/promptings/status/:status     # Lấy theo status

PUT    /api/promptings/bulk               # Bulk update
DELETE /api/promptings/bulk               # Bulk delete
```

**Usage Example**:
```typescript
// Tạo dictionary entry
const entry = await axios.post('/api/promptings', {
  LNG_TP: 'EN',
  COL_CD: 'GENDER',
  COL_CD_TP: 'M',
  COL_CD_TP_NM: 'Male',
  ACTIVE_YN: 1
});

// Lấy entries theo ngôn ngữ
const englishEntries = await axios.get('/api/promptings/language/EN');
```

### 3. 🔍 OCR System

**Mô tả**: Hệ thống nhận dạng ký tự quang học (Optical Character Recognition)

**Components**:
- **Service**: `src/database/services/ocr.service.ts`
- **Model**: `src/database/dto/OCRModel.ts`

**Tính năng**:
- ✅ Text extraction từ hình ảnh
- ✅ Document processing
- ✅ Multi-format support
- ✅ Accuracy optimization

### 4. 🛡️ Middleware System

**Mô tả**: Hệ thống middleware cho validation, authentication và logging

**Components**:
- **Chat Validation**: `src/middleware/chat-topic.validation.ts`
- **Promptings Validation**: `src/middleware/promptings.validation.ts`

**Tính năng**:
- ✅ Input validation
- ✅ Request sanitization
- ✅ Error handling
- ✅ Authentication checks
- ✅ Rate limiting

---

## 🔄 API Response Format

Tất cả APIs đều tuân theo format response chuẩn:

```typescript
interface APIResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors?: string[];
}
```

**Success Response**:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

**Error Response**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "Field is required",
    "Invalid format"
  ]
}
```

---

## 📄 Pagination Format

Tất cả list APIs đều hỗ trợ pagination với format chuẩn:

```typescript
interface PaginationResponse<T> {
  list: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
    nextPage: number | null;
    prevPage: number | null;
    sort: string;
    sortOrder: 'asc' | 'desc';
  };
}
```

**Query Parameters**:
- `page`: Số trang (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `sort`: Sort field (default: varies by endpoint)
- `sortOrder`: Sort order ('asc' | 'desc', default: 'desc')

---

## 🔐 Authentication & Security

### JWT Authentication
- Token-based authentication
- Configurable expiration
- Role-based access control

### Input Validation
- Comprehensive validation middleware
- SQL injection prevention
- XSS protection
- Data sanitization

### Error Handling
- Centralized error handling
- Detailed error logging
- User-friendly error messages
- Stack trace protection in production

---

## 📚 Documentation

### API Documentation
- **Chat APIs**: `src/docs/chat-api-documentation.md`
- **Promptings APIs**: `src/docs/promptings-api-documentation.md`

### Usage Examples
- **Chat Examples**: `src/examples/chat-api-usage.ts`
- **Promptings Examples**: `src/examples/promptings-api-usage.ts`

### Code Examples
Mỗi chức năng đều có examples chi tiết trong thư mục `src/examples/`

---

## 🏃‍♂️ Development Workflow

### 1. Setup Development Environment

```bash
# Clone repository
git clone <repository-url>
cd 3-fos-ekyc

# Install dependencies
npm install

# Setup environment
cp env/dev/.env.example env/dev/.env
# Edit env/dev/.env with your configurations

# Start development server
npm run dev
```

### 2. Code Structure Guidelines

**Service Layer** (`src/database/services/`):
- Business logic implementation
- Database operations
- Data validation
- Error handling

**Controller Layer** (`src/controllers/`):
- HTTP request/response handling
- Input validation
- Service layer orchestration
- Response formatting

**Route Layer** (`src/routes/`):
- API endpoint definitions
- Middleware integration
- Route documentation
- Parameter validation

**Middleware Layer** (`src/middleware/`):
- Request validation
- Authentication
- Authorization
- Error handling

### 3. Adding New Features

1. **Create Data Model** in `src/database/dto/`
2. **Implement Service** in `src/database/services/`
3. **Create Controller** in `src/controllers/`
4. **Add Validation** in `src/middleware/`
5. **Define Routes** in `src/routes/`
6. **Write Documentation** in `src/docs/`
7. **Create Examples** in `src/examples/`

---

## 🧪 Testing

### Test Structure
```
tests/
├── unit/           # Unit tests
├── integration/    # Integration tests
└── e2e/           # End-to-end tests
```

### Running Tests
```bash
# Run all tests
npm test

# Run specific test suite
npm run test:unit
npm run test:integration
npm run test:e2e

# Run with coverage
npm run test:coverage
```

---

## 🚀 Deployment

### Production Build
```bash
# Build for production
npm run build

# Start production server
npm start
```

### Environment Variables
Ensure all required environment variables are set in `env/pro/.env`:
- Database connection strings
- JWT secrets
- API keys
- Service endpoints

### Health Checks
- **Endpoint**: `GET /health`
- **Response**: System status and metrics

---

## 📊 Monitoring & Logging

### Logging System
- **Framework**: Winston
- **Levels**: error, warn, info, debug
- **Output**: Console + File
- **Rotation**: Daily log rotation

### Metrics
- API response times
- Database query performance
- Error rates
- System resource usage

---

## 🤝 Contributing

### Code Standards
- **Language**: TypeScript
- **Style**: ESLint + Prettier
- **Naming**: camelCase for variables, PascalCase for classes
- **Comments**: JSDoc for public APIs

### Pull Request Process
1. Create feature branch from `main`
2. Implement changes with tests
3. Update documentation
4. Submit pull request
5. Code review and approval
6. Merge to main

---

## 📞 Support & Contact

### Documentation
- **API Docs**: `/src/docs/`
- **Examples**: `/src/examples/`
- **Architecture**: This README

### Issues
- Report bugs via GitHub Issues
- Feature requests welcome
- Include reproduction steps

### Development Team
- **Backend**: Node.js + TypeScript
- **Database**: MongoDB
- **Architecture**: Microservices + REST APIs

---

## 📝 Changelog

### Version History
- **v1.0.0**: Initial release with Chat and Promptings systems
- **v1.1.0**: Added OCR functionality
- **v1.2.0**: Enhanced pagination and sorting
- **v1.3.0**: Added bulk operations

### Upcoming Features
- [ ] Real-time notifications
- [ ] Advanced analytics
- [ ] Mobile API optimizations
- [ ] GraphQL support

---

## 📄 License

This project is proprietary software. All rights reserved.

---

**Last Updated**: December 2024
**Version**: 1.3.0
**Maintainer**: Development Team
- Up source: 
  + dist
  + env
  + package.json (nếu có thay đổi)
  + ... Các thông tin khác nếu có thay đổi

- Restart app

--------------------------------------------------------------
# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###

* Quick summary
* Version
* [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up? ###

* Summary of set up
* Configuration
* Dependencies
* Database configuration
* How to run tests
* Deployment instructions

### Errors when run
- Delete node_modules/@types/prettier

### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact


### Bug/Error
- [Error: ENOENT: no such file or directory, open '/tmp/DDopvpV47L902YcG6QZv7O5v.jpg'] check folder /tmp and grant permission: chmod 777 tmp