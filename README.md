# Build
Kiểm tra thư mục env
`yarn build`

# Release 
- Up source: 
  + dist
  + env
  + package.json (nếu có thay đổi)
  + ... <PERSON><PERSON><PERSON> thông tin khác nếu có thay đổi

- Restart app

--------------------------------------------------------------
# README #

This README would normally document whatever steps are necessary to get your application up and running.

### What is this repository for? ###

* Quick summary
* Version
* [Learn Markdown](https://bitbucket.org/tutorials/markdowndemo)

### How do I get set up? ###

* Summary of set up
* Configuration
* Dependencies
* Database configuration
* How to run tests
* Deployment instructions

### Errors when run
- Delete node_modules/@types/prettier

### Contribution guidelines ###

* Writing tests
* Code review
* Other guidelines

### Who do I talk to? ###

* Repo owner or admin
* Other community or team contact


### Bug/Error
- [Error: ENOENT: no such file or directory, open '/tmp/DDopvpV47L902YcG6QZv7O5v.jpg'] check folder /tmp and grant permission: chmod 777 tmp