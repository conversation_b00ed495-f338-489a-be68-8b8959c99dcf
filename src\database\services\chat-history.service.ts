import logger from '../../config/logger';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopic, COLLECTION_CHAT_CUS_TOPIC  } from '../dto/ChatCusTopic';

interface FilterChat {
    filter: object,
    page: number,
    limit: number,
}

const checkVaild = (input:string) => {
    if(input == '' || input== undefined || input == null){
        return false;
    }
    return true;
}

const createChat = async (data: ChatCusTopic) => {
    try {
        if(checkVaild(data.fos_id) || checkVaild(data.topic_name)){
            return false;
        }
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);
        return result;

    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        throw error;
    }
}

const updateChat = async (fos_id: string, chat_id: string, data: Partial<ChatCusTopic>) => {
    try {
        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id, chat_id: chat_id },
            { $set: data }
        );
        logger.info(`ChatCusTopic updated for fos_id: ${fos_id}, chat_id: ${chat_id}`);
        return result;

    } catch (error) {
        logger.error('Error updating ChatCusTopic:', error);
        throw error;
    }
}

const deleteChat = async (fos_id: string, chat_id: string) => {
    try {
        const result = await databaseConnectionService.deleteOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id, chat_id: chat_id }
        );
        logger.info(`ChatCusTopic deleted for fos_id: ${fos_id}, chat_id: ${chat_id}`);
        return result;

    } catch (error) {
        logger.error('Error deleting ChatCusTopic:', error);
        throw error;
    }
}

const getChat = async (data: FilterChat) => {
    try {
        const result = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC,
            data.filter,
            data.page ?? 1,
            data.limit ?? 10
        );
        logger.info(`ChatCusTopic retrieved with filter: ${JSON.stringify(data.filter)}`);
        return result;

    } catch (error) {
        logger.error('Error retrieving ChatCusTopic:', error);
        throw error;
    }
}

// Lấy một chat topic cụ thể theo fos_id và chat_id
const getChatById = async (fos_id: string, chat_id: string) => {
    try {
        const result = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id, chat_id: chat_id }
        );
        logger.info(`ChatCusTopic retrieved for fos_id: ${fos_id}, chat_id: ${chat_id}`);
        return result;

    } catch (error) {
        logger.error('Error retrieving ChatCusTopic by ID:', error);
        throw error;
    }
}

// Xóa nhiều chat topics theo điều kiện
const deleteManyChats = async (filter: object) => {
    try {
        const result = await databaseConnectionService.deleteMany(
            COLLECTION_CHAT_CUS_TOPIC,
            filter
        );
        logger.info(`ChatCusTopic deleted many with filter: ${JSON.stringify(filter)}`);
        return result;

    } catch (error) {
        logger.error('Error deleting many ChatCusTopic:', error);
        throw error;
    }
}

// Cập nhật nhiều chat topics theo điều kiện
const updateManyChats = async (filter: object, updateData: Partial<ChatCusTopic>) => {
    try {
        const result = await databaseConnectionService.updateMany(
            COLLECTION_CHAT_CUS_TOPIC,
            filter,
            { $set: updateData }
        );
        logger.info(`ChatCusTopic updated many with filter: ${JSON.stringify(filter)}`);
        return result;

    } catch (error) {
        logger.error('Error updating many ChatCusTopic:', error);
        throw error;
    }
}

export default {
    createChat,
    updateChat,
    deleteChat,
    getChat,
    getChatById,
    deleteManyChats,
    updateManyChats,
}