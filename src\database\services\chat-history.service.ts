import logger from '../../config/logger';
import {  ObjectId }  from 'mongodb';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopic, ChatCusTopicMsg, COLLECTION_CHAT_CUS_TOPIC, COLLECTION_CHAT_CUS_TOPIC_MSG  } from '../dto/ChatCusTopic';
import {
    PaginationParams,
    PaginationResult,
    FilterWithPagination,
    calculatePagination,
    createSortObject
} from '../../interfaces/pagination.interface';
import { isUuidV4 } from './chat-topic-msg.service';

interface FilterChat {
    filter: object,
    page: number,
    limit: number,
    sort?: string,
    sortOrder?: 'asc' | 'desc',
    fos_id?: string,
}

export const TOPIC_EXPIRED_DATE = process.env.TOPIC_EXPIRED_DATE ?? "90" ;
export const TOPIC_MSG_MAX = process.env.TOPIC_MSG_MAX ?? "20" ;
export const TOPIC_DATA_STORAGE = process.env.TOPIC_DATA_STORAGE ?? "15" ;

const checkValid = (input: string) => {
    if (input == '' || input == undefined || input == null) {
        return false;
    }
    return true;
}

const createChat = async (data: ChatCusTopic) => {
    try {
        if (!checkValid(data.fos_id) || !checkValid(data.topic_name)) {
            logger.error('Invalid input data for ChatCusTopic');
            return { success: false, message: 'Invalid input data' };
        }

        data.created_at = new Date();
        data.updated_at = new Date();

        // lấy danh sách topic_id của các topic có tin nhắn mới nhất trên 90 ngày theo fos_id 
        const listTopicId = await databaseConnectionService.aggregate(COLLECTION_CHAT_CUS_TOPIC_MSG, [ 
            { $match: { fos_id: data.fos_id } },
            { $match: { "created_at": { $lt: new Date(Date.now() - (Number(TOPIC_EXPIRED_DATE) * 24 * 60 * 60 * 1000)) } } },   
            { $project: {_id: 0,  topic_id: "$topic_id" } }
        ])
        logger.info('Danh sách có tin nhắn cuối cùng trên 90 ngày: '+ listTopicId.length);
        logger.info('==> dữ liệu: '+ JSON.stringify(listTopicId));
        if(listTopicId.length > 0)cleanupOldData(listTopicId, data.fos_id);
        const listTopic = await databaseConnectionService.findMany<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: data.fos_id },
            { sort: { created_at: -1 }, skip:  Number(TOPIC_MSG_MAX)-1 , limit: 1000 }
        );
        
        logger.info('Danh sách có tin nhắn cuối cùng trên 20 tin nhắn: ', listTopic.length);
        if (listTopic.length > 0) { 
           let rsDelOver20 = await databaseConnectionService.deleteMany(
                COLLECTION_CHAT_CUS_TOPIC,
                { _id: { $in: listTopic.map((item:any) => isUuidV4(item._id)  ? item._id : new ObjectId(item._id)) } }
            );
            logger.info('TH2: quá '+Number(TOPIC_MSG_MAX)+' - Topic đã xóa: ', rsDelOver20);

            // lấy danh sách message có topic
            let listMessage = await databaseConnectionService.findMany<ChatCusTopicMsg>(
                COLLECTION_CHAT_CUS_TOPIC_MSG,
                { fos_id: data.fos_id, topic_id: { $in: listTopic.map((item:any) => item.topic_id) } }
            );

            let rsDelOver20Msg = await databaseConnectionService.deleteMany(
                COLLECTION_CHAT_CUS_TOPIC_MSG,
                { _id: { $in: listMessage.map((item:any) => new ObjectId(item._id)) } }
            );
            logger.info('TH2: quá '+Number(TOPIC_MSG_MAX)+' - message đã xóa: ', rsDelOver20Msg);
        }      
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );

        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);
        return { success: result.insertedId != null , data: result.insertedId};
    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        throw error;
    }
}

const updateChat = async (fos_id: string, topic_id: string, data: Partial<ChatCusTopic>) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id)) {
            logger.error('Invalid fos_id or topic_id');
            return { success: false, message: 'Invalid fos_id or topic_id' };
        }
        delete  data.fos_id;
        data.updated_at = new Date();
        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC, 
            { _id: isUuidV4(topic_id) ? topic_id :new ObjectId(topic_id), fos_id: fos_id},
            { $set: data }
        );
        logger.info(`ChatCusTopic updated for fos_id: ${fos_id}, topic_id: ${topic_id}`);
        return { success: result.modifiedCount == 1 };
    } catch (error) {
        logger.error('Error updating ChatCusTopic:', error);
        throw error;
    }
}

const deleteChat = async (fos_id: string, topic_id: string) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id)) {
            logger.error('Invalid fos_id or topic_id');
            return { success: false, message: 'Invalid fos_id or topic_id' };
        }
        const topicId = isUuidV4(topic_id) ? topic_id :new ObjectId(topic_id);
        // kiểm tra topic_id có thuộc fos_id không
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: topicId, fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }
        
        const result = await databaseConnectionService.deleteOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id, _id: topicId }
        );



        if(result.deletedCount == 1 ){
            // xóa chat msg 
            await databaseConnectionService.deleteMany(
                COLLECTION_CHAT_CUS_TOPIC_MSG,
                { fos_id: fos_id, topic_id: topic_id }
            );
        }
        logger.info(`ChatCusTopic deleted for fos_id: ${fos_id}, topic_id: ${topic_id}`);
        return { success: result.deletedCount == 1 };

    } catch (error) {
        logger.error('Error deleting ChatCusTopic:', error);
        throw error;
    }
}

const getChat = async (data: FilterChat): Promise<{ success: boolean, data?: any, message?: string }> => {
    try {
        const page = data.page ?? 1;
        const limit = data.limit ?? 10;
        const sort = data.sort ?? 'created_at';
        const sortOrder = data.sortOrder ?? 'desc';

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        // lấy danh sách topic_id của các topic có tin nhắn mới nhất trên 90 ngày theo fos_id 
        const listTopicId = await databaseConnectionService.aggregate(COLLECTION_CHAT_CUS_TOPIC_MSG, [ 
            { $match: { fos_id: data.fos_id } },
            { $match: { "created_at": { $lt: new Date(Date.now() - (Number(TOPIC_EXPIRED_DATE) * 24 * 60 * 60 * 1000)) } } },   
            { $project: {_id: 0,  topic_id: "$topic_id" } }
        ])
        logger.info('Danh sách có tin nhắn cuối cùng trên 90 ngày: '+ listTopicId.length);
        logger.info('==> dữ liệu: '+ JSON.stringify(listTopicId));
        if(listTopicId.length > 0) cleanupOldData(listTopicId, data.fos_id, true);

       
        // Get paginated data using existing method
        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC,
            data.filter,
            page,
            limit,
            sortObject
        );


        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`ChatCusTopic retrieved with filter: ${JSON.stringify(data.filter)}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopic:', error);
        throw error;
    }
}
// hàm clean 
async function cleanupOldData(listTopicId: any[], fos_id:string, size?: boolean) {
     try {
        // 1. Convert topic_id strings to ObjectId for the query
        // const topicObjectIds = listTopicId?.map((item: any) => new ObjectId(item.topic_id));
        const topicObjectIds = listTopicId?.map((item: any) => item.topic_id);

        if (!topicObjectIds || topicObjectIds.length === 0) {
            logger.info("No topic IDs provided for cleanup. Skipping deletion.");
            return;
        }

        // 2. Delete topics
        const deleteTopicResult = await databaseConnectionService.deleteMany(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: { $in: topicObjectIds.map((item: any) => isUuidV4(item) ? item :new ObjectId(item)) } }
        );
        logger.info(`TH1: Topic đã xóa: ${deleteTopicResult.deletedCount} documents.`);

        // 4. Find messages associated with the *original* list of topic IDs (before deletion of topics)
        // This is crucial: you're finding messages that *were* linked to the topics you just deleted.
        const listMessage = await databaseConnectionService.findMany<ChatCusTopicMsg>(
            COLLECTION_CHAT_CUS_TOPIC_MSG,
            { topic_id: { $in: topicObjectIds } }
        );
        logger.info(`==> Dữ liệu message tìm thấy để xóa: ${JSON.stringify(listMessage.length)} messages.`);

        if (listMessage.length > 0) {
            // 5. Delete messages based on the IDs of messages found
            const messageObjectIdsToDelete = listMessage.map((item: ChatCusTopicMsg) => item._id);
            const deleteMessageResult = await databaseConnectionService.deleteMany(
                COLLECTION_CHAT_CUS_TOPIC_MSG,
                { _id: { $in: messageObjectIdsToDelete } }
            );
            logger.info(`TH1: Message đã xóa: ${deleteMessageResult.deletedCount} documents.`);
        } else {
            logger.info("No messages found to delete for the specified topics.");
        }

        
          
    } catch (error) {
        logger.error('Error during cleanup process:', error);
        console.error(error); // Also log to console for immediate visibility
    }
}

// Cập nhật reaction cho topic
const updateTopicReaction = async (fos_id: string, topic_id: string, reaction: number) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id) || !checkValid(reaction.toString())) {
            logger.error('Invalid input params');
            return { success: false, message: 'Invalid input params' };
        }
        const topicId = isUuidV4(topic_id) ? topic_id :new ObjectId(topic_id);

        // kiểm tra topic_id có thuộc fos_id không
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: topicId, fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }

        // kiểm tra messageId exist
        const message = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: topicId}
        );
        if (!message) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }

        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id:  topicId },
            { $set: { reaction: reaction, updated_at: new Date() } }
        );
        logger.info(`ChatCusTopicMsg reaction updated for ID: ${topic_id}`);
        return { success: result.modifiedCount == 1 };
    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg reaction:', error);
        throw error;
    }
}
// Lấy một chat topic cụ thể theo fos_id và topic_id
// const getChatById = async (fos_id: string, topic_id: string) => {
//     try {
//         if (!checkValid(fos_id) || !checkValid(topic_id)) {
//             logger.error('Invalid fos_id or topic_id');
//             return { success: false, message: 'Invalid fos_id or topic_id' };
//         }

//         const result = await databaseConnectionService.findOne<ChatCusTopic>(
//             COLLECTION_CHAT_CUS_TOPIC,
//             { fos_id: fos_id, topic_id: topic_id }
//         );
//         logger.info(`ChatCusTopic retrieved for fos_id: ${fos_id}, topic_id: ${topic_id}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error retrieving ChatCusTopic by ID:', error);
//         throw error;
//     }
// }

// // Xóa nhiều chat topics theo điều kiện
// const deleteManyChats = async (filter: object) => {
//     try {
//         const result = await databaseConnectionService.deleteMany(
//             COLLECTION_CHAT_CUS_TOPIC,
//             filter
//         );
//         logger.info(`ChatCusTopic deleted many with filter: ${JSON.stringify(filter)}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error deleting many ChatCusTopic:', error);
//         throw error;
//     }
// }

// // Cập nhật nhiều chat topics theo điều kiện
// const updateManyChats = async (filter: object, updateData: Partial<ChatCusTopic>) => {
//     try {
//         updateData.updated_at = new Date();

//         const result = await databaseConnectionService.updateMany(
//             COLLECTION_CHAT_CUS_TOPIC,
//             filter,
//             { $set: updateData }
//         );
//         logger.info(`ChatCusTopic updated many with filter: ${JSON.stringify(filter)}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error updating many ChatCusTopic:', error);
//         throw error;
//     }
// }

// Lấy chat topics theo fos_id với phân trang cải tiến
const getChatsByFosId = async (
    fos_id: string,
    page: number = 1,
    limit: number = 10,
    sort: string = 'created_at',
    sortOrder: 'asc' | 'desc' = 'desc'
) => {
    try {
        if (!checkValid(fos_id)) {
            logger.error('Invalid fos_id');
            return { success: false, message: 'Invalid fos_id' };
        }

        // Tạo sort object cho MongoDB
        const sortObject = {
            [sort]: sortOrder === 'asc' ? 1 : -1
        };

        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id },
            page,
            limit,
            sortObject
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`ChatCusTopic retrieved for fos_id: ${fos_id}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopic by fos_id:', error);
        throw error;
    }
}

export default {
    createChat,
    updateChat,
    deleteChat,
    getChat,
    getChatsByFosId,
    updateTopicReaction,
    // getChatById,
    // deleteManyChats,
    // updateManyChats,
}
