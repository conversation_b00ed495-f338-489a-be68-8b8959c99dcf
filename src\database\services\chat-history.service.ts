import logger from '../../config/logger';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopic, COLLECTION_CHAT_CUS_TOPIC } from '../dto/ChatCusTopic';


const createChat = async (data: ChatCusTopic) =>{
    try {
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);

    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        
    }
}

const updateChat = async (data: ChatCusTopic) =>{
    try {
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);

    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        
    }
}
const deleteChat = async (data: ChatCusTopic) =>{
    try {
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);

    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        
    }
}
interface FilterChat {
    filter: object,
    page: number,
    limit: number,
}
const getChat = async (data: FilterChat) =>{
    try {
        const filter = {
            collectionName: COLLECTION_CHAT_CUS_TOPIC,
            filter: data.filter,
            page: data.page ?? 1,
            limit: 10,
            // sort?: any
        }
        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);

    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        
    }
}
// export const insert_log =(result:any)=>{
//     insertORC({
//         name: result.data[0].name  ?? result.data[0].mrz_details.name,
//         dob: result.data[0].dob ?? result.data[0].mrz_details.dob,
//         sex: result.data[0].sex,
//         id_card: result.data[0].id ?? result.data[0].mrz_details.id,
//         nationality: result.data[0].nationality ?? result.data[0].mrz_details.nationality,
//         home: result.data[0].home,
//         address: result.data[0].address,
//         overall_score: result.data[0].overall_score,
//         checking_result: result.data[0].checking_result,
//         json_data: result.data[0],
//         created_at: new Date(),
//         updated_at: new Date()
//     });
// }

export default {
    insert_log
}