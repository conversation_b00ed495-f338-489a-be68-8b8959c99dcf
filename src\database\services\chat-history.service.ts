import logger from '../../config/logger';
import {  ObjectId }  from 'mongodb';
import databaseConnectionService from '../DatabaseConnectionService';
import { ChatCusTopic, COLLECTION_CHAT_CUS_TOPIC, COLLECTION_CHAT_CUS_TOPIC_MSG  } from '../dto/ChatCusTopic';
import {
    PaginationParams,
    PaginationResult,
    FilterWithPagination,
    calculatePagination,
    createSortObject
} from '../../interfaces/pagination.interface';

interface FilterChat {
    filter: object,
    page: number,
    limit: number,
    sort?: string,
    sortOrder?: 'asc' | 'desc'
}

const checkValid = (input: string) => {
    if (input == '' || input == undefined || input == null) {
        return false;
    }
    return true;
}

const createChat = async (data: ChatCusTopic) => {
    try {
        if (!checkValid(data.fos_id) || !checkValid(data.topic_name)) {
            logger.error('Invalid input data for ChatCusTopic');
            return { success: false, message: 'Invalid input data' };
        }

        data.created_at = new Date();
        data.updated_at = new Date();

        const result = await databaseConnectionService.insert<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            data
        );
        logger.info(`ChatCusTopic created with ID: ${result.insertedId}`);
        return { success: result.insertedId != null };
    } catch (error) {
        logger.error('Error creating ChatCusTopic:', error);
        throw error;
    }
}

const updateChat = async (fos_id: string, topic_id: string, data: Partial<ChatCusTopic>) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id)) {
            logger.error('Invalid fos_id or topic_id');
            return { success: false, message: 'Invalid fos_id or topic_id' };
        }
        delete  data.fos_id;
        data.updated_at = new Date();
        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC, 
            { _id: new ObjectId(topic_id), fos_id: fos_id},
            { $set: data }
        );
        logger.info(`ChatCusTopic updated for fos_id: ${fos_id}, topic_id: ${topic_id}`);
        return { success: result.modifiedCount == 1 };
    } catch (error) {
        logger.error('Error updating ChatCusTopic:', error);
        throw error;
    }
}

const deleteChat = async (fos_id: string, topic_id: string) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id)) {
            logger.error('Invalid fos_id or topic_id');
            return { success: false, message: 'Invalid fos_id or topic_id' };
        }
        // kiểm tra topic_id có thuộc fos_id không
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: new ObjectId(topic_id), fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }
        
        const result = await databaseConnectionService.deleteOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { fos_id: fos_id, _id: new ObjectId(topic_id) }
        );
        if(result.deletedCount == 1 ){
            // xóa chat msg 
            await databaseConnectionService.deleteMany(
                COLLECTION_CHAT_CUS_TOPIC_MSG,
                { fos_id: fos_id, topic_id: topic_id }
            );
        }
        logger.info(`ChatCusTopic deleted for fos_id: ${fos_id}, topic_id: ${topic_id}`);
        return { success: result.deletedCount == 1 };

    } catch (error) {
        logger.error('Error deleting ChatCusTopic:', error);
        throw error;
    }
}

const getChat = async (data: FilterChat): Promise<{ success: boolean, data?: any, message?: string }> => {
    try {
        const page = data.page ?? 1;
        const limit = data.limit ?? 10;
        const sort = data.sort ?? 'created_at';
        const sortOrder = data.sortOrder ?? 'desc';

        // Get paginated data using existing method
        const paginatedResult = await databaseConnectionService.findWithPagination(
            COLLECTION_CHAT_CUS_TOPIC,
            data.filter,
            page,
            limit
        );

        // Enhanced pagination result with additional metadata
        const enhancedResult = {
            list: paginatedResult.data,
            pagination: {
                ...paginatedResult.pagination,
                sort: sort,
                sortOrder: sortOrder,
                hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
                hasPrevPage: paginatedResult.pagination.page > 1,
                nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
                prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
                currentPage: paginatedResult.pagination.page,
                totalPages: paginatedResult.pagination.pages,
                totalItems: paginatedResult.pagination.total,
                itemsPerPage: paginatedResult.pagination.limit
            }
        };

        logger.info(`ChatCusTopic retrieved with filter: ${JSON.stringify(data.filter)}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
        return { success: true, data: enhancedResult };

    } catch (error) {
        logger.error('Error retrieving ChatCusTopic:', error);
        throw error;
    }
}

// Cập nhật reaction cho topic
const updateTopicReaction = async (fos_id: string, topic_id: string, reaction: number) => {
    try {
        if (!checkValid(fos_id) || !checkValid(topic_id) || !checkValid(reaction.toString())) {
            logger.error('Invalid input params');
            return { success: false, message: 'Invalid input params' };
        }

        // kiểm tra topic_id có thuộc fos_id không
        const topic = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: new ObjectId(topic_id), fos_id: fos_id }
        );
        if (!topic) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }

        // kiểm tra messageId exist
        const message = await databaseConnectionService.findOne<ChatCusTopic>(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id: new ObjectId(topic_id) }
        );
        if (!message) {
            logger.error('Topic not found');
            return { success: false, message: 'Topic not found' };
        }

        const result = await databaseConnectionService.updateOne(
            COLLECTION_CHAT_CUS_TOPIC,
            { _id:  new ObjectId(topic_id), },
            { $set: { reaction: reaction, updated_at: new Date() } }
        );
        logger.info(`ChatCusTopicMsg reaction updated for ID: ${topic_id}`);
        return { success: result.modifiedCount == 1 };
    } catch (error) {
        logger.error('Error updating ChatCusTopicMsg reaction:', error);
        throw error;
    }
}
// Lấy một chat topic cụ thể theo fos_id và topic_id
// const getChatById = async (fos_id: string, topic_id: string) => {
//     try {
//         if (!checkValid(fos_id) || !checkValid(topic_id)) {
//             logger.error('Invalid fos_id or topic_id');
//             return { success: false, message: 'Invalid fos_id or topic_id' };
//         }

//         const result = await databaseConnectionService.findOne<ChatCusTopic>(
//             COLLECTION_CHAT_CUS_TOPIC,
//             { fos_id: fos_id, topic_id: topic_id }
//         );
//         logger.info(`ChatCusTopic retrieved for fos_id: ${fos_id}, topic_id: ${topic_id}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error retrieving ChatCusTopic by ID:', error);
//         throw error;
//     }
// }

// // Xóa nhiều chat topics theo điều kiện
// const deleteManyChats = async (filter: object) => {
//     try {
//         const result = await databaseConnectionService.deleteMany(
//             COLLECTION_CHAT_CUS_TOPIC,
//             filter
//         );
//         logger.info(`ChatCusTopic deleted many with filter: ${JSON.stringify(filter)}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error deleting many ChatCusTopic:', error);
//         throw error;
//     }
// }

// // Cập nhật nhiều chat topics theo điều kiện
// const updateManyChats = async (filter: object, updateData: Partial<ChatCusTopic>) => {
//     try {
//         updateData.updated_at = new Date();

//         const result = await databaseConnectionService.updateMany(
//             COLLECTION_CHAT_CUS_TOPIC,
//             filter,
//             { $set: updateData }
//         );
//         logger.info(`ChatCusTopic updated many with filter: ${JSON.stringify(filter)}`);
//         return { success: true, data: result };

//     } catch (error) {
//         logger.error('Error updating many ChatCusTopic:', error);
//         throw error;
//     }
// }

// // Lấy chat topics theo fos_id với phân trang cải tiến
// const getChatsByFosId = async (
//     fos_id: string,
//     page: number = 1,
//     limit: number = 10,
//     sort: string = 'created_at',
//     sortOrder: 'asc' | 'desc' = 'desc'
// ) => {
//     try {
//         if (!checkValid(fos_id)) {
//             logger.error('Invalid fos_id');
//             return { success: false, message: 'Invalid fos_id' };
//         }

//         const paginatedResult = await databaseConnectionService.findWithPagination(
//             COLLECTION_CHAT_CUS_TOPIC,
//             { fos_id: fos_id },
//             page,
//             limit
//         );

//         // Enhanced pagination result with additional metadata
//         const enhancedResult = {
//             ...paginatedResult,
//             list: paginatedResult.data,
//             pagination: {
//                 ...paginatedResult.pagination,
//                 sort: sort,
//                 sortOrder: sortOrder,
//                 hasNextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages,
//                 hasPrevPage: paginatedResult.pagination.page > 1,
//                 nextPage: paginatedResult.pagination.page < paginatedResult.pagination.pages ? paginatedResult.pagination.page + 1 : null,
//                 prevPage: paginatedResult.pagination.page > 1 ? paginatedResult.pagination.page - 1 : null,
//                 currentPage: paginatedResult.pagination.page,
//                 totalPages: paginatedResult.pagination.pages,
//                 totalItems: paginatedResult.pagination.total,
//                 itemsPerPage: paginatedResult.pagination.limit
//             }
//         };

//         logger.info(`ChatCusTopic retrieved for fos_id: ${fos_id}, page: ${page}, limit: ${limit}, sort: ${sort} ${sortOrder}`);
//         return { success: true, data: enhancedResult };

//     } catch (error) {
//         logger.error('Error retrieving ChatCusTopic by fos_id:', error);
//         throw error;
//     }
// }

export default {
    createChat,
    updateChat,
    deleteChat,
    getChat,
    updateTopicReaction,
    // getChatById,
    // getChatsByFosId,
    // deleteManyChats,
    // updateManyChats,
}