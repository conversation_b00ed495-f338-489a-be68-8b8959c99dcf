import axios from 'axios';
import logger from '../config/logger';
import IRequest from 'IRequest';

const baseUrl = `${process.env.INFORBIP_URL}/trading-view`;

interface ISaveList {
  user_id: string;
  name?: string;
  symbol?: string;
  resolution?: string;
  content: string;
  description?: string;
}
class TradingViewService {
  public getSavedList = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        if (!req
                    || !req.query
                    || !req.query.hasOwnProperty('user')) {
          return resolvePr({
            data: [],
            messageErr: 'Call error query params not valid',
            status: 'FAILED',
          });
        }

        const { user, chart } = req.query;

        const configCall = {
          method: 'get',
          maxBodyLength: Infinity,
          url: chart ? `${baseUrl}/detail` : baseUrl,
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            user,
            chart,
            client: 'webtrading',
          },
        };
         // @ts-ignore
        await axios.request(configCall)
                    .then((response) => {
                      console.log(configCall, response)

                      logger.info(JSON.stringify(response.data));
                      const { status, message, data } = response.data;

                      return resolvePr({ status, data, messageErr: message });
                    })
                    .catch((error) => {
                      logger.info(error);
                      return resolvePr({ data: [], messageErr: JSON.stringify(error) });
                    });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }

  public saveList = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        const contentType = req.headers['content-type'];
        const boundary = contentType.split('boundary=')[1];

        if (!boundary || !req.query.user) {
          return {
            data: [],
            status: 'ERROR',
            success: false,
            message: 'Content-Type is not multipart/form-data',
          };
        }

        const rawData = await new Promise((resolve, reject) => {
          const chunks: any[] = [];
          req.on('data', (chunk: any[]) => chunks.push(chunk));
          req.on('end', () => resolve(Buffer.concat(chunks)));
          req.on('error', (err: any) => reject(err));
        });

        const parts = rawData.toString().split(`--${boundary}`);

        const formData: ISaveList = {
          content: '',
          user_id: req.query.user as string,
        };

        for (const part of parts) {
          if (part.includes('Content-Disposition')) {
            const nameMatch = part.match(/name="(.+?)"/);
            if (nameMatch) {
              const name: string = nameMatch[1];
              // @ts-ignore
              formData[name] = part.split('\r\n\r\n')[1]?.trim();
            }
          }
        }

        const { client, user } = req.query;

        const configCall = {
          data: formData,
          method: 'post',
          maxBodyLength: Infinity,
          url: `${baseUrl}`,
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            client,
            user,
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              logger.info(JSON.stringify(response.data));
              console.log({ response: response.data })

              const { status, message, data } = response.data;

              return resolvePr({ status, data, messageErr: message });
            })
            .catch((error) => {
              logger.info(error);
              return resolvePr({ data: [], messageErr: JSON.stringify(error) });
            });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }

  public deleteChart = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        const { client, user, chart } = req.query;

        const configCall = {
          method: 'delete',
          maxBodyLength: Infinity,
          url: `${baseUrl}`,
          headers: {
            'Content-Type': 'application/json',
          },
          params: {
            client,
            user,
            chart,
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              logger.info(JSON.stringify(response.data));
              console.log({ response: response.data })

              const { status, message, data } = response.data;

              return resolvePr({ status, data, messageErr: message });
            })
            .catch((error) => {
              logger.info(error);
              return resolvePr({ data: [], messageErr: JSON.stringify(error) });
            });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }
}

const insTradingViewService = new TradingViewService();
export default insTradingViewService;
