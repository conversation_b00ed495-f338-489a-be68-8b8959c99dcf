// Pagination interfaces for consistent pagination across the application

export interface PaginationParams {
    page: number;
    limit: number;
    sort?: string;
    sortOrder?: 'asc' | 'desc';
}

export interface PaginationQuery {
    page?: string | number;
    limit?: string | number;
    sort?: string;
    sortOrder?: 'asc' | 'desc';
}

export interface PaginationResult<T> {
    items: T[];
    pagination: {
        currentPage: number;
        totalPages: number;
        totalItems: number;
        itemsPerPage: number;
        hasNextPage: boolean;
        hasPrevPage: boolean;
        nextPage: number | null;
        prevPage: number | null;
    };
}

export interface FilterWithPagination {
    filter: object;
    pagination: PaginationParams;
}

// Utility function to parse pagination parameters
export const parsePaginationParams = (query: PaginationQuery): PaginationParams => {
    const page = Math.max(1, parseInt(String(query.page || 1)));
    const limit = Math.min(100, Math.max(1, parseInt(String(query.limit || 10))));
    const sort = query.sort || 'created_at';
    const sortOrder = query.sortOrder === 'asc' ? 'asc' : 'desc';

    return {
        page,
        limit,
        sort,
        sortOrder
    };
};

// Utility function to calculate pagination metadata
export const calculatePagination = (
    totalItems: number,
    currentPage: number,
    itemsPerPage: number
) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const hasNextPage = currentPage < totalPages;
    const hasPrevPage = currentPage > 1;
    const nextPage = hasNextPage ? currentPage + 1 : null;
    const prevPage = hasPrevPage ? currentPage - 1 : null;

    return {
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage,
        hasNextPage,
        hasPrevPage,
        nextPage,
        prevPage
    };
};

// Utility function to create sort object for MongoDB
export const createSortObject = (sort: string, sortOrder: 'asc' | 'desc') => {
    return {
        [sort]: sortOrder === 'asc' ? 1 : -1
    };
};
