// db.ts
import { MongoClient, Db, Collection } from 'mongodb';
import logger from '../config/logger';

export class MongoDBClient {
  private static client: MongoClient;
  private static db: Db;

  private constructor() {} // Prevent instantiation

  public static async connect(uri: string, dbName: string) {
    if (!this.client) {
      this.client = new MongoClient(uri);
      await this.client.connect();
      logger.info('✅ Connected to MongoDB');
      this.db = this.client.db(dbName);
    }
    return this.db;
  }

  public static getDb(): Db {
    if (!this.db) {
      throw new Error('MongoDB not connected. Call connect() first.');
    }
    return this.db;
  }

  public static getCollection<T>(collectionName: string): Collection<T> {
    return this.getDb().collection<T>(collectionName);
  }

  public static async disconnect() {
    if (this.client) {
      await this.client.close();
      logger.info('🔌 MongoDB disconnected');
      this.client = null!;
      this.db = null!;
    }
  }
}
