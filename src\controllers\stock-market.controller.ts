import IController from 'IController';
import apiResponse from '../utilities/apiResponse';
import insStockMarketService from '../services/StockMarketService';
import process from 'process';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';

const hashSHA256 = process.env.STOCK_MARKET_HEADER || '85680a1aeb82a95c561838e9bb93102872fdbdc59a9b4e69d162c0dd1e241fb9'

const getListStockMarket: IController = async (req, res): Promise<void> => {
  const { headers } = req;
  if (!headers['access-token-authen']
      || (headers['access-token-authen']
          && headers['access-token-authen'] !== hashSHA256)
  ) {
    apiResponse.error(
        res,
        httpStatusCodes.FORBIDDEN,
    );
    return
  }

  try {
    const result = await insStockMarketService.getStockMarket(req);
    apiResponse.result(res, result);
    return;
  } catch (error) {
    console.log('error: ', error);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
}

export default { getListStockMarket }
