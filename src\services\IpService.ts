import logger from '../config/logger';
import IRequest from 'IRequest';

class IpService {
  public getIpAddress = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        return resolvePr({ data: req.socket.remoteAddress });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }

}

const insIpService = new IpService();
export default insIpService;
