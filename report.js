const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');
const moment = require('moment');

const headerReport = {
    // số request client gọi api ekyc
    client_request_total: 'Tổng số request client gọi api ekyc',
    // số request server xử lý lỗi
    request_server_error: 'Số request server xử lý lỗi',
    // số request sai cấu trúc
    client_request_invalid: 'Số request sai cấu trúc',

    // số request bước 1
    client_request_1: 'Số request bước 1',
    // số request sai cấu trúc bước 1
    client_request_invalid_1: 'Số request sai cấu trúc bước 1',
    // số request gọi fpt bước 1
    request_fpt_1: 'Số request gọi fpt bước 1',
    // số request fpt trả thành công bước 1
    respone_fpt_success_1: 'S<PERSON> request fpt trả thành công bước 1',
    // số request fpt trả thất bại bước 1
    respone_fpt_failure_1: 'Số request fpt trả thất bại bước 1',
    // số request pass logic ekyc bước 1
    check_logic_success_1: 'Số request pass logic ekyc bước 1',
    // số request fail logic ekyc bước 1
    check_logic_failure_1: 'Số request fail logic ekyc bước 1',
    // số request phản hồi client bước 1
    respone_client_1: 'Số request phản hồi client bước 1',

    // tương tự cho bước 2
    client_request_2: 'Số request bước 2',
    client_request_invalid_2: 'Số request sai cấu trúc bước 2',
    request_fpt_2: 'Số request gọi fpt bước 2',
    respone_fpt_success_2: 'Số request fpt trả thành công bước 2',
    respone_fpt_failure_2: 'Số request fpt trả thất bại bước 2',
    check_logic_success_2: 'Số request pass logic ekyc bước 2',
    check_logic_failure_2: 'Số request fail logic ekyc bước 2',
    respone_client_2: 'Số request phản hồi client bước 2',

    // tương tự cho bước 3
    client_request_3: 'Số request bước 3',
    client_request_invalid_3: 'Số request sai cấu trúc bước 3',
    request_fpt_3: 'Số request gọi fpt bước 3',
    respone_fpt_success_3: 'Số request fpt trả thành công bước 3',
    respone_fpt_failure_3: 'Số request fpt trả thất bại bước 3',
    check_logic_success_3: 'Số request pass logic ekyc bước 3',
    check_logic_failure_3: 'Số request fail logic ekyc bước 3',
    respone_client_3: 'Số request phản hồi client bước 3',

    // tổng số request phản hồi client
    respone_client_total: 'Tổng số request phản hồi client',

    client_device: 'Request từ thiết bị'
};

const dataXlsx = (list) => {

    return list.map((data, index) => {
        let tempData = {};
        tempData[headerReport.client_device] = data.client_device

        tempData[headerReport.client_request_1] = data.client_request_1;
        tempData[headerReport.client_request_invalid_1] =
            data.client_request_invalid_1;
        tempData[headerReport.request_fpt_1] = data.request_fpt_1;
        tempData[headerReport.respone_fpt_success_1] =
            data.respone_fpt_success_1;
        tempData[headerReport.respone_fpt_failure_1] =
            data.respone_fpt_failure_1;
        tempData[headerReport.check_logic_success_1] =
            data.check_logic_success_1;
        tempData[headerReport.check_logic_failure_1] =
            data.check_logic_failure_1;
        tempData[headerReport.respone_client_1] = data.respone_client_1;

        tempData[headerReport.client_request_2] = data.client_request_2;
        tempData[headerReport.client_request_invalid_2] =
            data.client_request_invalid_2;
        tempData[headerReport.request_fpt_2] = data.request_fpt_2;
        tempData[headerReport.respone_fpt_success_2] =
            data.respone_fpt_success_2;
        tempData[headerReport.respone_fpt_failure_2] =
            data.respone_fpt_failure_2;
        tempData[headerReport.check_logic_success_2] =
            data.check_logic_success_2;
        tempData[headerReport.check_logic_failure_2] =
            data.check_logic_failure_2;
        tempData[headerReport.respone_client_2] = data.respone_client_2;

        tempData[headerReport.client_request_3] = data.client_request_3;
        tempData[headerReport.client_request_invalid_3] =
            data.client_request_invalid_3;
        tempData[headerReport.request_fpt_3] = data.request_fpt_3;
        tempData[headerReport.respone_fpt_success_3] =
            data.respone_fpt_success_3;
        tempData[headerReport.respone_fpt_failure_3] =
            data.respone_fpt_failure_3;
        tempData[headerReport.check_logic_success_3] =
            data.check_logic_success_3;
        tempData[headerReport.check_logic_failure_3] =
            data.check_logic_failure_3;
        tempData[headerReport.respone_client_3] = data.respone_client_3;

        
        return tempData
    })
};

const exportXlsx = (data) => {
    const worksheet = XLSX.utils.json_to_sheet(dataXlsx(data));
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    const buf = XLSX.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx',
    });
    fs.writeFileSync(
        `./Report Ekyc -  ${moment().format('DDMMYYYY')}.xlsx`,
        buf,
    );
    console.log('Xuất báo cáo thành công!')
};

async function getJsonData() {
    const jsonsInDir = fs
        .readdirSync('./report')
        .filter((file) => path.extname(file) === '.json');

    const result = [];

    jsonsInDir.forEach((file) => {
        const fileData = fs.readFileSync(path.join('./report', file));
        const json = JSON.parse(fileData.toString());

        Object.keys(json).forEach((item) => {
            const isInfoClientDevice = result.find(
                (e) => e.client_device === item,
            );
            if (isInfoClientDevice) {
                isInfoClientDevice.client_request_1 +=
                    (json[item].client_request_1 || 0);
                isInfoClientDevice.client_request_invalid_1 +=
                    (json[item].client_request_invalid_1 || 0);
                isInfoClientDevice.request_fpt_1 +=
                    (json[item].request_fpt_1 || 0);
                isInfoClientDevice.respone_fpt_success_1 +=
                    (json[item].respone_fpt_success_1 || 0);
                isInfoClientDevice.respone_fpt_failure_1 +=
                    (json[item].respone_fpt_failure_1 || 0);
                isInfoClientDevice.check_logic_success_1 +=
                    (json[item].check_logic_success_1 || 0);
                isInfoClientDevice.check_logic_failure_1 +=
                    (json[item].check_logic_failure_1 || 0);
                isInfoClientDevice.respone_client_1 +=
                    (json[item].respone_client_1 || 0);

                isInfoClientDevice.client_request_2 +=
                    (json[item].client_request_2 || 0);
                isInfoClientDevice.client_request_invalid_2 +=
                    (json[item].client_request_invalid_2 || 0);
                isInfoClientDevice.request_fpt_2 +=
                    (json[item].request_fpt_2 || 0);
                isInfoClientDevice.respone_fpt_success_2 +=
                    (json[item].respone_fpt_success_2 || 0);
                isInfoClientDevice.respone_fpt_failure_2 +=
                    (json[item].respone_fpt_failure_2 || 0);
                isInfoClientDevice.check_logic_success_2 +=
                    (json[item].check_logic_success_2 || 0);
                isInfoClientDevice.check_logic_failure_2 +=
                    (json[item].check_logic_failure_2 || 0);
                isInfoClientDevice.respone_client_2 +=
                    (json[item].respone_client_2 || 0);

                isInfoClientDevice.client_request_3 +=
                    (json[item].client_request_3 || 0);
                isInfoClientDevice.client_request_invalid_3 +=
                    (json[item].client_request_invalid_3 || 0);
                isInfoClientDevice.request_fpt_3 +=
                    (json[item].request_fpt_3 || 0);
                isInfoClientDevice.respone_fpt_success_3 +=
                    (json[item].respone_fpt_success_3 || 0);
                isInfoClientDevice.respone_fpt_failure_3 +=
                    (json[item].respone_fpt_failure_3 || 0);
                isInfoClientDevice.check_logic_success_3 +=
                    (json[item].check_logic_success_3 || 0);
                isInfoClientDevice.check_logic_failure_3 +=
                    (json[item].check_logic_failure_3 || 0);
                isInfoClientDevice.respone_client_3 +=
                    (json[item].respone_client_3 || 0);
            } else {
                const newInfo = {
                    client_request_1: 0,
                    client_request_invalid_1: 0,
                    request_fpt_1: 0,
                    respone_fpt_success_1: 0,
                    respone_fpt_failure_1: 0,
                    check_logic_success_1: 0,
                    check_logic_failure_1: 0,
                    respone_client_1: 0,

                    client_request_2: 0,
                    client_request_invalid_2: 0,
                    request_fpt_2: 0,
                    respone_fpt_success_2: 0,
                    respone_fpt_failure_2: 0,
                    check_logic_success_2: 0,
                    check_logic_failure_2: 0,
                    respone_client_2: 0,

                    client_request_3: 0,
                    client_request_invalid_3: 0,
                    request_fpt_3: 0,
                    respone_fpt_success_3: 0,
                    respone_fpt_failure_3: 0,
                    check_logic_success_3: 0,
                    check_logic_failure_3: 0,
                    respone_client_3: 0,
                };
                newInfo.client_request_1 +=
                    (json[item].client_request_1 || 0);
                newInfo.client_request_invalid_1 +=
                    (json[item].client_request_invalid_1 || 0);
                newInfo.request_fpt_1 += (json[item].request_fpt_1 || 0);
                newInfo.respone_fpt_success_1 +=
                    (json[item].respone_fpt_success_1 || 0);
                newInfo.respone_fpt_failure_1 +=
                    (json[item].respone_fpt_failure_1 || 0);
                newInfo.check_logic_success_1 +=
                    (json[item].check_logic_success_1 || 0);
                newInfo.check_logic_failure_1 +=
                    (json[item].check_logic_failure_1 || 0);
                newInfo.respone_client_1 +=
                    (json[item].respone_client_1 || 0);

                newInfo.client_request_2 +=
                    (json[item].client_request_2 || 0);
                newInfo.client_request_invalid_2 +=
                    (json[item].client_request_invalid_2 || 0);
                newInfo.request_fpt_2 += (json[item].request_fpt_2 || 0);
                newInfo.respone_fpt_success_2 +=
                    (json[item].respone_fpt_success_2 || 0);
                newInfo.respone_fpt_failure_2 +=
                    (json[item].respone_fpt_failure_2 || 0);
                newInfo.check_logic_success_2 +=
                    (json[item].check_logic_success_2 || 0);
                newInfo.check_logic_failure_2 +=
                    (json[item].check_logic_failure_2 || 0);
                newInfo.respone_client_2 +=
                    (json[item].respone_client_2 || 0);

                newInfo.client_request_3 +=
                    (json[item].client_request_3 || 0);
                newInfo.client_request_invalid_3 +=
                    (json[item].client_request_invalid_3 || 0);
                newInfo.request_fpt_3 += (json[item].request_fpt_3 || 0);
                newInfo.respone_fpt_success_3 +=
                    (json[item].respone_fpt_success_3 || 0);
                newInfo.respone_fpt_failure_3 +=
                    (json[item].respone_fpt_failure_3 || 0);
                newInfo.check_logic_success_3 +=
                    (json[item].check_logic_success_3 || 0);
                newInfo.check_logic_failure_3 +=
                    (json[item].check_logic_failure_3 || 0);
                newInfo.respone_client_3 +=
                    (json[item].respone_client_3 || 0);
                newInfo.client_device = item;
                result.push(newInfo);
            }
        });
    });
    console.log('dataReport', result);
    exportXlsx(result);
}

getJsonData();
