import axios, { AxiosRequestConfig } from "axios"
import path from 'path';
import * as fs from 'fs'
import logger from '../config/logger';
const jwt = require('jsonwebtoken');
const moment = require('moment');

// cấu hình chung
const pathJsonFA = path.join(process.cwd(), './JsonAuth/token-fa.json');
class FAToken {
    private accessToken: string;
    private refreshToken: string;

    constructor(accessToken:string, refreshToken:string ) {
        this.accessToken = accessToken;
        this.refreshToken = refreshToken;
    }

    public getToken = (): string =>{
        return this.accessToken;
    }

    public getRefreshToken = (): string =>{
        return this.refreshToken;
    }

    public checkTokenValid = ():boolean =>{
        let flag = true;
        try {
            logger.info("--------> FA: Bắt đầu kiểm tra token <--------");
            const parseToken = jwt.decode(this.accessToken);
            if(!parseToken) return false;
            const exp = parseToken.exp * 1000;
            let tokenDate = moment(exp);    
            if (!tokenDate.isValid())
                flag = false;
            let now = moment();
            if (now > tokenDate) {
                 logger.info("FA: token hết hạn >>> " + tokenDate);
                flag = false;
            }
            return flag;
        } catch (error) {
            console.log('error: ', error);
            logger.info("--------> FA: Lỗi kiểm tra token : " + JSON.stringify(error) + " <--------");
            return false
        }finally{
            logger.info("--------> FA: Kết thúc kiểm tra token : "+flag+"  <--------");
        }
    }
}

/**
 * FAService: xử lý logic
 * loginFA: đăng nhập lấy token từ FA
 * refreshTokenFA: lấy token từ FA dùng refreshToken
 * getTokenFA: Trả token cho client SSV
 */
class FAService {

    private setMemCache = (tokenStore: FAToken): Promise<any> => {
        return new Promise(async (resolve) => {
            try {
                let data = JSON.stringify(tokenStore);
                fs.writeFileSync(pathJsonFA, data);
                logger.info("Store Token ok");
                return resolve("ok");

            } catch (error) {
                logger.info("Store Token Error");
                logger.info(error);
                resolve(error);
            }
        }).catch(error => {
            logger.info(error);
        })
    }

    private readMemCache = async (): Promise<FAToken> => {
        // @ts-ignore
        return new Promise(async (resolve) => {
            try {
                if (fs.existsSync(pathJsonFA)) {
                    fs.readFile(pathJsonFA,
                        (_err: any, data: any) => {
                            let dataRefresh = JSON.parse(data);
                            logger.info(dataRefresh);
                            if (dataRefresh && dataRefresh.hasOwnProperty('accessToken')) {
                                logger.info("Set value from token cache");
                                resolve(new FAToken(dataRefresh.accessToken,dataRefresh.refreshToken));
                            }
                        });
                } else {
                    resolve(new FAToken("",""));
                }

            } catch (error) {
                logger.info(error);
                resolve(new FAToken("",""));
            }
        }).catch(error => {
            logger.info( "readMemCache: " + JSON.stringify(error));
        })

    }

    public async loginFA(username: string, password: string): Promise<any> {
        try {
            const hostname = process.env.FA_HOSTNAME || "https://restv2.fireant.vn"
            const config: AxiosRequestConfig = {
                method: "POST",
                url: hostname + "/authentication/login",
                headers: {
                    "Content-Type": "application/json",
                },
                timeout: 60000,
                data: {
                    email: username,
                    password: password
                }
            }
            const result = await axios.request(config);
            let token = null;
            let refreshToken = null;

            if (result.status == 200 && result.data.succeeded) {
                token = result.data.accessToken;
                refreshToken = result.data.refreshToken;
                return {
                    success: true,
                    data: {
                        token,
                        refreshToken
                    }
                }
            }
            return {
                success: false,
                message: result.data.errorMessage,
                data: {
                    token: null,
                    refreshToken: null
                }
            }
        } catch (error) {
            console.log('error: ', error);
            return {
                code: 500,
                message: error.message,
                success: false               
            }
        }
    }

    public async refreshTokenFA(isRefreshToken: string): Promise<any> {
        try {
            const hostname = process.env.FA_HOSTNAME || "https://restv2.fireant.vn"
            const config: AxiosRequestConfig = {
                method: "POST",
                url: hostname + "/authentication/refresh-token",
                headers: {
                    "Content-Type": "application/json",
                },
                timeout: 60000,
                data: {
                    refreshToken: isRefreshToken
                }
            }   
            const result = await axios.request(config);
            let token = null;
            let refreshToken = null;
            if (result.status == 200 && result.data.succeeded) {
                token = result.data.token;
                refreshToken = result.data.refreshToken;
                return {
                    success: true,
                    data: {
                        token,
                        refreshToken
                    }
                }
            }
            return {
                success: false,
                message: result.data.errorMessage,
                data: {
                    token: null,
                    refreshToken: null
                }
            }
        } catch (error) {
            console.log('error: ', error);
            return {
                code: 500,
                success: false               
            }
        }
    }

    public async getTokenFA(): Promise<any> {
        try {
            let seft = this;
            let tokenCache = await seft.readMemCache();
            if(tokenCache.checkTokenValid()){
                return {
                    success: true,
                    data: tokenCache.getToken()
                }
            }
            // gọi API login và cập nhật lại
            logger.info("FA: Token hết hạn, gọi API login để lấy token mới");
            const FA_EMAIL = process.env.FA_EMAIL || "<EMAIL>"
            const FA_PASSWORD = process.env.FA_PASSWORD || "Shinhan@123456";
            const rsLogin = await this.loginFA(FA_EMAIL, FA_PASSWORD);
            if(rsLogin.success){
                const tokenStore = new FAToken(rsLogin.data.token, rsLogin.data.refreshToken);
                seft.setMemCache(tokenStore);
                return {success: true, data: rsLogin.data.token}
            }
            return {success: false, message: rsLogin.message}
        }           
        catch (error) {
            return {
                code: 500,
                success: false               
            }
        }
    }   

}

const insFAService = new FAService()
export default insFAService