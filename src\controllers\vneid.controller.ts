import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';
import vneidService from '../services/vneidService'
import { CommonFormatImage, CommonTypeImage, TImage, ICardInfoReq } from '../types/vneid';
// Tạo Key API 
// Kiểm tra dữ liệu thiếu t
/**
 * Mã lỗi: 400
 * - Thiếu trường dữ liệu: 
 *   + base64:
 *   + type: back | front | selfie | qrvnid
 *   + format: jpeg | jpg | png
 *   + FOSId:
 * Mã lỗi: 401 - APIKEY không có
 * Mã lỗi: 403 - APIKEY không đúng
 * Mã lỗi khác: Hệ thống xử lý lỗi 
 */

const hashSHA256 = process.env.VNEID_HEADER || "25d6a9f6620134608c2357d14cae3e27cea537f784765746b72d7674a6d9c561" //10VaniAlpha2023

// API Kiểm tra BCA
const fs = require("fs");
import path from 'path';
import _ from 'lodash';
const ERROR_CODE = {
    "api_error": "Hệ thống đang lỗi. Vui lòng thử lại.",
    "headers_invalid": "Headers invalid.",
    "idcard_invalid": "IdCard invalid.",
    "cardinfo_invalid": "cardInfo invalid.",
    "api_parse_error": "Call API Parse Card Infor: Error",
    "api_read_nfc_error": "Call API Read NFC: Error"
}
const SHA256_CHECK_CARD = process.env.VNEID_HEADER_CHECK_CARD || "3afde58cec881b0a1dbf2539f9185448c44a63b6729ec9729eea09fb6d260c38" //08ShinhhanVNeID-BCA-2024


const uploadImage : IController = async (req, res) => {   
    //  Kiểm tra tính ATTT 
    try {
        const { body, headers }= req;
        logger.info(JSON.stringify(body));
        logger.info("-----------------Start UploadImage VNeID-----------------------");
        logger.info('body, headers: ', body, headers);
        if( !headers['x-vneid-x'] || headers['x-vneid-x'] && headers['x-vneid-x'] != hashSHA256){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
            ); 
            return   
        }
        // kiểm tra dữ liệu
        const {base64, type, fosid, formatImage} = body
        if(!base64 || !type || !fosid) return apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST, 
        ); 
        if(!CommonTypeImage.includes(type)) return apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST, 
        ); 
        if(!CommonFormatImage.includes(formatImage)) return apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST, 
        ); 
        // Đóng gói body xử lý
        const param : TImage = {
            base64: base64,
            type: type,
            fosid: fosid,
            format: formatImage
        }

        // xử lý
        const result : any = await vneidService.uploadImage(param)
        logger.info(JSON.stringify(result));

        // kết quả trả về
        if(!result){
            apiResponse.result(res, { 
                responseServer: 'ok',
                response: {
                    filename: null,
                    error: "Upload Image Failed"
                }            
             });
        }
        apiResponse.result(res, { 
            responseServer: 'Success',
            response: {
                filename: result,
            }            
         });
        logger.info("-----------------End UploadImage VNeID-----------------------");
    } catch (e) {
        logger.info("----------------Error End UploadImage VNeID-----------------------");
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }


} 

const downloadImage : IController = async (req, res) =>{
    try {
        const { query, headers }= req;
        logger.info(JSON.stringify(query));
        logger.info("-----------------Start downloadImage VNeID-----------------------");
        logger.info('body, headers: ', query, headers);
        if( !headers['x-vneid-x'] || headers['x-vneid-x'] && headers['x-vneid-x'] != hashSHA256){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
            ); 
            return   
        }

        // kiểm tra dữ liệu
        const {fosid, filename} = query
        if( !fosid || !filename || filename && filename.toString().trim().length  == 0){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST, 
            ); 
            return   
        }   
        // kiểm fosid xem có tồn tại Không 
        const checkFilename = filename.toString().trim().split('_')
        if(!checkFilename.includes(fosid.toString())){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                "Input Invalid" 
            ); 
            return
        }
        const result: any = await vneidService.downloadImage(filename.toString(), fosid.toString())
        if(!result.success){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST, 
                result['message'] 
            );  
            return   
        }
        apiResponse.result(res, { 
            responseServer: 'Success',
            response: {
                base64: result,
                filename: filename,
                fosid: fosid
            }            
         });
        logger.info("-----------------End downloadImage VNeID-----------------------");
    } catch (error) {
        logger.info("----------------Error End downloadImage VNeID-----------------------");
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
}

// API LOGIN get accessToken
const loginGetAccessToken :IController = async (req, res) => {
    try {
        logger.info("\n\n----------------Start loginGetAccessToken VNeID-----------------------");
        const result = await vneidService.GetAccessToken();
        console.log('result: ', result);
        if(result.success){
            // Thànhc ông
            apiResponse.result(res, { 
                responseServer: 'Success',
                response: result, 
            }); 
        }else{
            // 400 lỗi API 
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                result.message              
            );
        }
        logger.info("----------------End loginGetAccessToken VNeID-----------------------");
    } catch (error) {
         // 500 hệ thống
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            ERROR_CODE['api_error'],
        );
        logger.info("----------------Error End loginGetAccessToken VNeID-----------------------");
    }
}

const readNFC: IController = async (req, res) => {
    logger.info("\n\n----------------Start readNFC VNeID-----------------------");
    try {
        const { body, headers }= req;
        // 1. Kiểm tra tính hợp lệ của headers
        const API_KEY_BCA = headers['ssv-authorization'] || null;
        if(_.isNil(API_KEY_BCA) || _.isUndefined(API_KEY_BCA) || !_.isNil(API_KEY_BCA) && API_KEY_BCA != SHA256_CHECK_CARD ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['headers_invalid'],
            );
            return
        }

        const { idCard, cardInfo} = body;
        // 2. Kiểm tra tính hợp lệ ID-Card
        if(_.isNil(idCard) || !_.isNil(idCard) && idCard.length != 12 ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['idcard_invalid'] ,
            );
            return
        }
        // kiểm tra cardInfo có hợp lệ hay không
        if(_.isNil(cardInfo) || _.isUndefined(cardInfo) || _.isEmpty(cardInfo) ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['cardinfo_invalid'] ,
            );
            return 
        }
        // GỌI API FPT
        const param:ICardInfoReq = cardInfo;
        const config = {
            idCard: idCard,
            // pathUrl: pathUrl
        }
        const result = await vneidService.ReadNFC(param, config);
        logger.info('result: '+ JSON.stringify(result));
        if(!result.success){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                result.message,
            );
            return
        }
        apiResponse.result(res, { 
            responseServer: 'Successed',
            response: result.data, 
            message: result.message,
            status: result.code
         });
        logger.info("----------------END readNFC VNeID-----------------------\n\n");
        return 
    }catch(error){
        logger.info("----------------Error END readNFC VNeID-----------------------\n\n");
    }
}

const verifyNFC : IController = async (req, res) => {
    logger.info("\n\n----------------Start VerifyNFC VNeID-----------------------");
    try {
        const { body, headers }= req;
        // 1. Kiểm tra tính hợp lệ của headers
        const API_KEY_BCA = headers['ssv-authorization'] || null;
        if(_.isNil(API_KEY_BCA) || _.isUndefined(API_KEY_BCA) || !_.isNil(API_KEY_BCA) && API_KEY_BCA != SHA256_CHECK_CARD ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['headers_invalid'],
            );
            return
        }

        const { cardInfo} = body;
        let idCard = body.idCard || ''

        // 2. Kiểm tra tính hợp lệ ID-Card
        if(_.isNil(idCard) || !_.isNil(idCard) && idCard.length != 12 ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['idcard_invalid'] ,
            );
            return
        }
        // kiểm tra cardInfo có hợp lệ hay không
        if(_.isNil(cardInfo) || _.isUndefined(cardInfo) || _.isEmpty(cardInfo) ){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['cardinfo_invalid'] ,
            );
            return 
        }

        // 3. Kiểm tra folder chứa hợp lệ 
        // kiểm vneid /CCCD / res_time - req_timer
        let pathUrl =  process.env.DB_LOCAL || 'DataLocal';
        let cpPathUrl = '';
        pathUrl = pathUrl.toString().trim().replace("/","");
        // kiểm tra folder 'vneid'
        if(!fs.existsSync(path.join(pathUrl,''))){
            fs.mkdirSync(path.join(pathUrl, ''));
        }   
        pathUrl = pathUrl + '/' + 'vneid';
        cpPathUrl = pathUrl;

        // kiểm tra folder 'vneid'
        if(!fs.existsSync(path.join(pathUrl,''))){
            fs.mkdirSync(path.join(pathUrl, ''));
        }   
        pathUrl = pathUrl + '/' + idCard;
         // kiểm tra folder 'vneid'
        if(!fs.existsSync(path.join(pathUrl,''))){
            fs.mkdirSync(path.join(pathUrl, ''));
        } 
        const pathUrlNfc = pathUrl + '/nfc'
        if(!fs.existsSync(path.join(pathUrlNfc,''))){
            fs.mkdirSync(path.join(pathUrlNfc, ''));
        } 
        // kiểm tra có req/res folder
        if(!fs.existsSync(path.join(pathUrlNfc, 'req'))){
            fs.mkdirSync(path.join(pathUrlNfc, 'req'));
        }
        if(!fs.existsSync(path.join(pathUrlNfc, 'res'))){
            fs.mkdirSync(path.join(pathUrlNfc, 'res'));
        }    
        pathUrl = pathUrl + '/' + 'pc06';
        // kiểm tra folder 'vneid'
        if(!fs.existsSync(path.join(pathUrl,''))){
           fs.mkdirSync(path.join(pathUrl, ''));
        }   

       
        // kiểm tra có req/res folder
        if(!fs.existsSync(path.join(pathUrl, 'req'))){
            fs.mkdirSync(path.join(pathUrl, 'req'));
        }
        if(!fs.existsSync(path.join(pathUrl, 'res'))){
            fs.mkdirSync(path.join(pathUrl, 'res'));
        }    

       

        // 2.2. Kiểm tra với thông tin được mã hoá. gọi API Read information
        const rsReadNFC = await vneidService.ReadNFC(cardInfo,{  
            checkValidIdCard: true,
            cardInfo: cardInfo,
            idCard: idCard,
            pathUrl: pathUrlNfc
        });
        logger.info("rsReadNFC: " + JSON.stringify(rsReadNFC)+"\n");
        if(!rsReadNFC.success){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                rsReadNFC.message,
            );
            return
        }
        // Kiểm tra CC
        let message = "";
        if(rsReadNFC?.data?.exitCode != 0){
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                rsReadNFC?.data?.data?.exitCodeMessage,
            );
            return
        }
        if(rsReadNFC?.data?.data?.citizenPid != undefined && rsReadNFC.data.data.citizenPid != idCard){
            idCard = rsReadNFC.data.citizenPid
            message += "Số CCCD/Căn cước sau khi mã hoá khác nhau " + idCard + " | " +rsReadNFC.data.data.citizenPid;
            console.log('>>>>>>>>> message: ', message);
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                ERROR_CODE['idcard_invalid'],
            );
            return
        }
        // GỌI API FPT
        const param: ICardInfoReq = cardInfo;
        const config = {
            idCard: idCard,
            pathUrl: pathUrl
        }
        const resVerifyNFC = await vneidService.VerifyNFC(param, config );
        logger.info("resVerifyNFC: " + JSON.stringify(resVerifyNFC)+"\n");

        if(!resVerifyNFC.success){

            logger.info("---------------- Error End VerifyNFC VNeID-----------------------");
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                resVerifyNFC.message,
            );
            return
        }
        logger.info('resVerifyNFC: '+ JSON.stringify(resVerifyNFC));
        apiResponse.result(res, { 
            responseServer: 'Success verify VNeIDCard',
            response: {
                readData:rsReadNFC.data,
                verifyData: resVerifyNFC.idCheck,
                verifyStatus: rsReadNFC.data.exitCode == resVerifyNFC.idCheck.exitCode && resVerifyNFC.idCheck.exitCode == 0
            }, 
            message: message
        });
        logger.info("----------------End VerifyNFC VNeID-----------------------");
    } catch (error) {
        logger.info('error: ' + error);
        logger.info("----------------Error End VerifyNFC VNeID-----------------------");
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            ERROR_CODE['api_error'],
        );
    }
}

export default {
    uploadImage,
    downloadImage,
    loginGetAccessToken,
    readNFC,
    verifyNFC
}   
