import * as express from 'express';

import ekyc from './ekyc/ekyc.route'
import ekycImage from './ekyc-image/ekyc-image.route'
import econtract from  './econtract/econtract.route';
import tokenca from  './tokenca/tokenca.route';
import bankRoute from './bank/bank.route';
import signRoute from './sign/sign.route';
import copyRoute from './copy/copy.route';
import foreignRoute from './foreign-cust/foreign-cust.route';
import docRoute from './doc/doc.route';
import googleToken from './google-token/google-token.route';
import report from './report/report.route';
import vaniOpen from './vani-open/vani-open.route';
import vneid from './vneid/vneid.route';
import suggestAccounts from './suggest-account-number/suggest-account-number.route';
import badWordFilter from './badword-filter/badword-filter.route';
import uploadAvatar from './upload-avatar/upload-avatar.route';
import stockMarket from './stock-market/stock-market.route';
import tradingView from './trading-view/trading-view.route'
import videoCall from './video-call/uploadImageCall.route';
import uploadFile from './upload-file/uploadFile.route';
import IPCheckHealthy from './ip/ip-route';

import HistoryChatRoutes from './history-chat/chat-topic.routes';
import promptingsRoutes from './history-chat/promptings.routes';
import fireantRoutes from './fireant/fireant.route';
const router = express.Router();

router.use('/ekyc', ekyc);
router.use('/ekyc-image', ekycImage);
router.use('/econtract', econtract);
router.use('/tokenca', tokenca);
router.use('/sol', bankRoute);
router.use('/sign', signRoute);
router.use('/sync', copyRoute);
router.use('/foreign', foreignRoute);
router.use('/doc', docRoute);
router.use('/google', googleToken);
router.use('/report', report);
router.use('/vani', vaniOpen);
router.use('/vneid', vneid);
router.use('/account', suggestAccounts);
router.use('/badword', badWordFilter);
router.use('/avatar', uploadAvatar);
router.use('/stock-market', stockMarket);
router.use('/tradingview/1.1/charts', tradingView);
router.use('/upload', uploadFile);
router.use('/video-call', videoCall);

router.use('/check-healthy', IPCheckHealthy);
router.use('/chat', HistoryChatRoutes);
router.use('/promptings', promptingsRoutes);
router.use('/fireant', fireantRoutes);

export default router;
