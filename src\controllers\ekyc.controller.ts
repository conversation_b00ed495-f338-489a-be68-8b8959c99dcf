import httpStatusCodes from 'http-status-codes';

import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import locale from '../constants/locale';
import glb_sv from '../share/global_service';
const fs = require('fs')
import multiparty from 'multiparty';
import rimraf from 'rimraf';
import insFptService from '../services/FptService';
import path from 'path';
import moment from 'moment';
import { IEkycSchema } from '../constants/schema/ekyc.schema';
import { createFaceSearchCollectionID } from './utils/createFaceSearchCollectionID';
import logger from '../config/logger';
import sharp from 'sharp';
import { cloneDeep } from 'lodash';

import axios from 'axios';
import { insert_log } from '../database/services/ocr.service';

const hashSHA256 =
    process.env.AVATAR_UPLOAD_HEADER ||
    '85680a1aeb82a95c561838e9bb93102872fdbdc59a9b4e69d162c0dd1e241fb9';

const checkStep: IController = async (req, res) => {
    let userId = req.body.userId;
    if (!userId) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
        return;
    }
    let SecCode = req.body.sec;
    if (!glb_sv.LIST_SEC.includes(SecCode)) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
        return;
    }

    if (glb_sv.checkPathUser(SecCode, userId)) {
        const pathUser = glb_sv.getPathUser(SecCode, userId);
        try {
            const arrResult: any = [];
            if (!fs.existsSync(path.join(pathUser, `/front_id`))) {
                apiResponse.result(res, {
                    result: [],
                });
                return;
            }
            if (
                fs.existsSync(
                    path.join(pathUser, `/front_id/front_id.json`),
                )
            ) {
                const json = getJson(
                    path.join(pathUser, `/front_id/front_id.json`),
                );
                if (!res) {
                    apiResponse.result(res, {
                        result: arrResult,
                    });
                    return;
                }
                delete json.data?.[0]?.face_base64;
                const resCheck = glb_sv.checkEkyc('1', SecCode, json);
                // resCheck.data[0].cropped_idcard_base64 = ''
                if (resCheck.data[0].errorCode === 0) {
                    arrResult.push(resCheck.data[0]);
                } else {
                    apiResponse.result(res, {
                        result: arrResult,
                    });
                    return;
                }
            }
            if (!fs.existsSync(path.join(pathUser, `/back_id`))) {
                apiResponse.result(res, {
                    result: arrResult,
                });
                return;
            }
            if (
                fs.existsSync(
                    path.join(pathUser, `/back_id/back_id.json`),
                )
            ) {
                const json = getJson(
                    path.join(pathUser, `/back_id/back_id.json`),
                );
                if (!json) {
                    apiResponse.result(res, {
                        result: arrResult,
                    });
                    return;
                }
                delete json.data?.[0]?.face_base64;
                const resCheck = glb_sv.checkEkyc('2', SecCode, json);
                // resCheck.data[0].cropped_idcard_base64 = ''
                if (resCheck.data[0].errorCode === 0) {
                    arrResult.push(resCheck.data[0]);
                } else {
                    apiResponse.result(res, {
                        result: arrResult,
                    });
                    return;
                }
            }
            if (!fs.existsSync(path.join(pathUser, `/video_selfie`))) {
                apiResponse.result(res, {
                    result: arrResult,
                });
                return;
            }
            if (
                fs.existsSync(
                    path.join(
                        pathUser,
                        `/video_selfie/video_selfie.json`,
                    ),
                )
            ) {
                const json = getJson(
                    path.join(
                        pathUser,
                        `/video_selfie/video_selfie.json`,
                    ),
                );
                if (!json) {
                    apiResponse.result(res, {
                        result: arrResult,
                    });
                    return;
                }
                const resCheck = glb_sv.checkEkyc('3', SecCode, json);
                // resCheck.image_face = ''
                if (resCheck.errorCode === 0) arrResult.push(resCheck);
                else {
                    apiResponse.result(res, {
                        result: [],
                    });
                    return;
                }
            }
            apiResponse.result(res, {
                result: arrResult,
            });
        } catch (err) {
            console.log(
                '🚀 ~ file: ekyc.controller.ts checkStep error',
                err,
            );
            apiResponse.result(res, {
                result: [],
            });
        }
    } else {
        apiResponse.result(res, {
            result: [],
        });
    }
};

const upload: IController = async (req, res) => {
    glb_sv.dataReportOther.client_request_total++;
    glb_sv.logDataReportOther();
    const form = new multiparty.Form();
    form.parse(req, async function (err, fields, files) {
        if (err) {
            glb_sv.dataReportOther.request_server_error++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReportOther();
            logger.error(`SERVER_ERROR form.parse: ${err}`, fields);
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
            return;
        }
        logger.info(`Request upload`, fields);

        if (
            !fields.userId ||
            !fields.sec ||
            !fields.fileName ||
            !fields.step
        ) {
            glb_sv.dataReportOther.client_request_invalid++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: userId sec fileName step`,
                fields,
            );
            return;
        }

        if (!fields.MWLoginID || !fields.MdmTp) {
            logger.info(`REQUEST_WARNING: MWLoginID or MdmTp`, fields);
        }

        const MWLoginID = fields.MWLoginID
            ? fields.MWLoginID[0]
            : 'other';
        const MdmTp = fields.MdmTp ? fields.MdmTp[0] : 'other';

        let step = fields.step[0];
        if (!['1', '2', '3'].includes(step)) {
            glb_sv.dataReportOther.client_request_invalid++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(`REQUEST_ERROR: step(${step})`);
            return;
        }
        if (!glb_sv.dataReport[`${MWLoginID}_${MdmTp}`]) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`] = {};
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'request_fpt_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'request_fpt_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_fpt_success_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_fpt_success_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_fpt_failure_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_fpt_failure_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'check_logic_success_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'check_logic_success_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'check_logic_failure_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'check_logic_failure_' + step
            ] = 0;
        }
        if (
            !glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ] = 0;
        }

        glb_sv
            .dataReport[`${MWLoginID}_${MdmTp}`]['client_request_' + step]++;

        let userId = fields.userId[0];
        if (!userId) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(`REQUEST_ERROR: userId(${userId})`);
            return;
        }
        let SecCode = fields.sec[0];
        if (!glb_sv.LIST_SEC.includes(SecCode)) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(`REQUEST_ERROR: LIST_SEC(${SecCode})`);
            return;
        }
        if (
            !fields.fileName ||
            !fields.fileName.length ||
            !fields.fileName[0]
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: fileName(${fields.fileName[0]})`,
            );
            return;
        }
        let fileName = fields.fileName[0];
        let isImageFaceMatch = fields.isImageFaceMatch
            ? fields.isImageFaceMatch[0]
            : false;

        if (['1', '2'].includes(step) && !glb_sv.isImage(fileName)) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: file extension is not image fileName(${fileName}) for step(${step})`,
            );
            return;
        }
        if (
            step === '3' &&
            !glb_sv.isVideo(fileName) &&
            !isImageFaceMatch
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: fileName extension is not video(${fileName}) for step(${step}) isImageFaceMatch(${isImageFaceMatch})`,
            );
            return;
        }
        if (
            step === '3' &&
            !glb_sv.isImage(fileName) &&
            isImageFaceMatch
        ) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: fileName extension is not image(${fileName}) for step(${step}) isImageFaceMatch(${isImageFaceMatch})`,
            );
            return;
        }
        if (!files.file || !files.file.length) {
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'client_request_invalid_' + step
            ]++;
            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                'respone_client_' + step
            ]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            logger.error(
                `REQUEST_ERROR: file upload null ${JSON.stringify(
                    files.file,
                )}`,
            );
            return;
        }
        let nameFile = fileName;
        let nameFileFace = '', nameImageFromVideo = '';
        if (step === '1') {
            nameFile =
                '/front_id/front_id.' + glb_sv.getTypeImage(fileName);
        }
        if (step === '2') {
            nameFile =
                '/back_id/back_id.' + glb_sv.getTypeImage(fileName);
        }
        if (step === '3') {
            if (!fields.fileNameFace || !fields.fileNameFace.length) {
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'client_request_invalid_' + step
                ]++;
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'respone_client_' + step
                ]++;
                glb_sv.dataReportOther.respone_client_total++;
                glb_sv.logDataReport();
                glb_sv.logDataReportOther();
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                logger.error(
                    `REQUEST_ERROR: fileNameFace(${fields.fileName[0]})`,
                );
                return;
            }
            if (!files.fileFace || !files.fileFace.length) {
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'client_request_invalid_' + step
                ]++;
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'respone_client_' + step
                ]++;
                glb_sv.dataReportOther.respone_client_total++;
                glb_sv.logDataReport();
                glb_sv.logDataReportOther();
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                logger.error(
                    `REQUEST_ERROR: fileFace upload null ${JSON.stringify(
                        files.fileFace,
                    )}`,
                );
                return;
            }
            let fileNameFace = fields.fileNameFace[0];
            if (!fileNameFace || !glb_sv.isImage(fileNameFace)) {
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'client_request_invalid_' + step
                ]++;
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'respone_client_' + step
                ]++;
                glb_sv.dataReportOther.respone_client_total++;
                glb_sv.logDataReport();
                glb_sv.logDataReportOther();
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                logger.error(
                    `REQUEST_ERROR: fileNameFace extension is not image(${fileNameFace})`,
                );
                return;
            }
            nameFile =
                '/video_selfie/video_selfie.' +
                glb_sv.getTypeImage(fileName);
            nameFileFace =
                '/video_selfie/image_selfie.' +
                glb_sv.getTypeImage(fileNameFace);
            //24052023 PhuongNH overwrite filename nameImageFromVideo begin
            /*nameImageFromVideo =
                '/video_selfie/video_selfie_frame.jpg';*/
            nameImageFromVideo = nameFileFace;
            //24052023 Fix overwrite filename nameImageFromVideo end

        }

        try {
            let isWriteVideoDone = false,
                isWriteImageDone = false;
            const pathFile =
                glb_sv.getPathUser(SecCode, userId) + nameFile;
            const dir = glb_sv.getPathUser(SecCode, userId);
            const file = files.file[0];
            if (!file || !file.path) {
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'client_request_invalid_' + step
                ]++;
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'respone_client_' + step
                ]++;
                glb_sv.dataReportOther.respone_client_total++;
                glb_sv.logDataReport();
                glb_sv.logDataReportOther();
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                logger.error(
                    `REQUEST_ERROR: file upload null file(${file}) file.path(${file.path})`,
                );
                return;
            }
            if (step === '1') {
                if (!fs.existsSync(dir + '/front_id'))
                    fs.mkdirSync(dir + '/front_id');
                else {
                    if (fs.existsSync(dir + '/front_id'))
                        rimraf.sync(dir + '/front_id');
                    if (fs.existsSync(dir + '/back_id'))
                        rimraf.sync(dir + '/back_id');
                    if (fs.existsSync(dir + '/video_selfie'))
                        rimraf.sync(dir + '/video_selfie');
                    fs.mkdirSync(dir + '/front_id');
                }
            }
            if (step === '2') {
                if (!fs.existsSync(dir + '/back_id'))
                    fs.mkdirSync(dir + '/back_id');
                else {
                    if (fs.existsSync(dir + '/back_id'))
                        rimraf.sync(dir + '/back_id');
                    if (fs.existsSync(dir + '/video_selfie'))
                        rimraf.sync(dir + '/video_selfie');
                    fs.mkdirSync(dir + '/back_id');
                }
            }
            if (step === '3') {
                if (!fs.existsSync(dir + '/video_selfie'))
                    fs.mkdirSync(dir + '/video_selfie');
                else {
                    if (fs.existsSync(dir + '/video_selfie'))
                        rimraf.sync(dir + '/video_selfie');
                    fs.mkdirSync(dir + '/video_selfie');
                }
            }

            const reduceFace =
                step === '3'
                    ? {
                          buffer: fs.readFileSync(file.path),
                          base64: '',
                      }
                    : await glb_sv.resizeImage(file.path, 1000);
            if (!reduceFace.base64 && ['1', '2'].includes(step)) {
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'client_request_invalid_' + step
                ]++;
                glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                    'respone_client_' + step
                ]++;
                glb_sv.dataReportOther.respone_client_total++;
                glb_sv.logDataReport();
                glb_sv.logDataReportOther();
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                return;
            }
            const ws = fs.createWriteStream(`${pathFile}`);
            ws.write(reduceFace.buffer);
            ws.close();

            ws.once('finish', async () => {
                isWriteVideoDone = true;
                if (isWriteImageDone || step !== '3') {
                    const infoReq = {
                        sec: SecCode,
                        userId: userId,
                        step: step,
                        fileName: fileName,
                    };
                    try {
                        logger.info(
                            `REQUEST_API_FPT_EKYC: infoReq(${JSON.stringify(
                                infoReq,
                            )}) pathFile(${pathFile})`,
                        );
                        glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                            'request_fpt_' + step
                        ]++;
                        glb_sv.logDataReport();
                        const resEkyc = await insFptService.call(
                            infoReq,
                            pathFile,
                        );
                        glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                            'respone_fpt_success_' + step
                        ]++;
                        glb_sv.logDataReport();
                        const cloneResEkyc = cloneDeep(resEkyc);
                        if (
                            cloneResEkyc &&
                            cloneResEkyc.data &&
                            cloneResEkyc.data[0]
                        ) {
                            cloneResEkyc.data[0].face_base64 = '';
                            cloneResEkyc.data[0].cropped_idcard_base64 =
                                '';
                        }
                        logger.info(
                            `RESPONE_API_FPT_EKYC: resEkyc(${JSON.stringify(
                                cloneResEkyc,
                            )})`,
                        );
                        // NhacVB R3301
                        if(step ==='3' && resEkyc !=undefined && resEkyc.liveness != undefined && (resEkyc.liveness.video_photo_base64 != undefined 
                         || resEkyc.liveness.video_photo_base64 != null  ||(resEkyc.liveness.video_photo_base64 && resEkyc.liveness.video_photo_base64.toString().Trim().length >0 )  )
                        )                        
                        // if (step === '3' && resEkyc.liveness?.video_photo_base64) 
                        {
                            const pathFileFace =
                                glb_sv.getPathUser(SecCode, userId) + 
                                nameFileFace;
                            resEkyc.image_face =
                                base64_encode(pathFileFace);
                            const pathImageFromVideo =
                                glb_sv.getPathUser(SecCode, userId) +
                                nameImageFromVideo;
                            let bufferImageFromVideo = Buffer.from(resEkyc.liveness.video_photo_base64, 'base64')
                            //PhuongNH check size  buffer before  write file begin
                            // fs.writeFile(
                            //     pathImageFromVideo,
                            //     bufferImageFromVideo,
                            //     (err) => {},
                            // );
                            if(bufferImageFromVideo && bufferImageFromVideo.length > 0) {
                                logger.info('fileName ' + pathImageFromVideo + ' size buffer ' + bufferImageFromVideo.length);
                                fs.writeFile(
                                    pathImageFromVideo,
                                    bufferImageFromVideo,
                                    (err:any) => {},
                                );
                            }
                            //PhuongNH check size  buffer before  write file end
                        }
                        glb_sv.saveFileJson(infoReq, resEkyc);

                        const resCheckEkyc = glb_sv.checkEkyc(
                            step,
                            SecCode,
                            resEkyc,
                        );
                        const flatObjectData = glb_sv.getResModel(
                            infoReq,
                            '1',
                            resCheckEkyc,
                        );
                        if (flatObjectData.errorCode === 0) {
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'check_logic_success_' + step
                            ]++;
                        } else {
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'check_logic_failure_' + step
                            ]++;
                        }
                        glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                            'respone_client_' + step
                        ]++;
                        glb_sv.dataReportOther.respone_client_total++;
                        glb_sv.logDataReport();
                        glb_sv.logDataReportOther();

                        logger.info(
                            `CHECK_LOGIC_EKYC: flatObjectData errorCode(${flatObjectData.errorCode}) errorMessage(${flatObjectData.errorMessage})`,
                        );
                        // flatObjectData.cropped_idcard_base64 = ''
                        // flatObjectData.image_face = ''
                        flatObjectData.face_base64 = '';
                        apiResponse.result(res, flatObjectData);
                        // Xong bước một thì tạo user face search bên FPT luôn sau khi xong bước 3 thì sẽ add face
                        createUserInFPTFaceSearch({
                            fosid: userId,
                            nameOfUser: resEkyc?.data[0]?.name || '',
                            seccode: SecCode,
                        });
                        // ---------------------------------------------------
                    } catch (err) {
                        glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                            'respone_fpt_failure_' + step
                        ]++;
                        glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                            'respone_client_' + step
                        ]++;
                        glb_sv.dataReportOther.respone_client_total++;
                        glb_sv.logDataReport();
                        glb_sv.logDataReportOther();

                        logger.error(
                            'SERVER_ERROR: ws.once(finish) -> isWriteImageDone || step !== 3',
                            { error: err },
                        );
                        apiResponse.error(
                            res,
                            httpStatusCodes.INTERNAL_SERVER_ERROR,
                            locale.API_ALT_ERROR,
                        );
                    }
                }
            });

            if (step === '3') {
                const fileFace = files.fileFace[0];
                const pathFileFace =
                    glb_sv.getPathUser(SecCode, userId) + nameFileFace;
                const reduceFace = await glb_sv.resizeImage(
                    fileFace.path,
                );
                if (!reduceFace.base64) {
                    glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                        'client_request_invalid_' + step
                    ]++;
                    glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                        'respone_client_' + step
                    ]++;
                    glb_sv.dataReportOther.respone_client_total++;
                    glb_sv.logDataReport();
                    glb_sv.logDataReportOther();
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.REQUEST_ERROR,
                    );
                    return;
                }
                const ws = fs.createWriteStream(`${pathFileFace}`);
                ws.write(reduceFace.buffer);
                ws.close();

                ws.once('finish', async () => {
                    isWriteImageDone = true;
                    if (isWriteVideoDone) {
                        const infoReq = {
                            sec: SecCode,
                            userId: userId,
                            step: step,
                            fileName: fileName,
                            isImageFaceMatch,
                        };
                        try {
                            logger.info(
                                `REQUEST_API_FPT_EKYC: infoReq(${JSON.stringify(
                                    infoReq,
                                )}) pathFile(${pathFile})`,
                            );
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'request_fpt_' + step
                            ]++;
                            glb_sv.logDataReport();
                            const resEkyc = await insFptService.call(
                                infoReq,
                                pathFile,
                            );
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'respone_fpt_success_' + step
                            ]++;
                            glb_sv.logDataReport();
                            const cloneResEkyc = cloneDeep(resEkyc);
                            if (cloneResEkyc) {
                                cloneResEkyc.image_face = '';
                            }
                            logger.info(
                                `RESPONE_API_FPT_EKYC: resEkyc(${JSON.stringify(
                                    cloneResEkyc,
                                )})`,
                            );
                            resEkyc.image_face = reduceFace.base64;
                            if (resEkyc.liveness?.video_photo_base64) {
                                const pathImageFromVideo =
                                    glb_sv.getPathUser(SecCode, userId) +
                                    nameImageFromVideo;
                                let bufferImageFromVideo = Buffer.from(resEkyc.liveness?.video_photo_base64 || '', 'base64')

                                //PhuongNH check size  buffer before  write file begin
                                // fs.writeFile(
                                //     pathImageFromVideo,
                                //     bufferImageFromVideo,
                                //     (err) => {},
                                // );
                                if (bufferImageFromVideo && bufferImageFromVideo.length > 0) {
                                    logger.info('fileName ' + pathImageFromVideo + ' size buffer ' + bufferImageFromVideo.length);
                                    fs.writeFile(
                                        pathImageFromVideo,
                                        bufferImageFromVideo,
                                        (err:any) => {},
                                    );
                                }
                                //PhuongNH check size  buffer before  write file end
                            }

                            glb_sv.saveFileJson(infoReq, resEkyc);
                            const resCheckEkyc = glb_sv.checkEkyc(
                                step,
                                SecCode,
                                resEkyc,
                                isImageFaceMatch,
                            );
                            const flatObjectData = glb_sv.getResModel(
                                infoReq,
                                '1',
                                resCheckEkyc,
                            );
                            logger.info(
                                `CHECK_LOGIC_EKYC: flatObjectData errorCode(${flatObjectData.errorCode}) errorMessage(${flatObjectData.errorMessage})`,
                            );
                            // flatObjectData.cropped_idcard_base64 = ''
                            // flatObjectData.image_face = ''
                            flatObjectData.face_base64 = '';
                            if (flatObjectData.errorCode === 0) {
                                glb_sv.dataReport[
                                    `${MWLoginID}_${MdmTp}`
                                ]['check_logic_success_' + step]++;
                            } else {
                                glb_sv.dataReport[
                                    `${MWLoginID}_${MdmTp}`
                                ]['check_logic_failure_' + step]++;
                            }
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'respone_client_' + step
                            ]++;
                            glb_sv.dataReportOther
                                .respone_client_total++;
                            glb_sv.logDataReport();
                            glb_sv.logDataReportOther();

                            apiResponse.result(res, flatObjectData);

                            // -------------------  --------------------------------
                        } catch (err) {
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'respone_fpt_failure_' + step
                            ]++;
                            glb_sv.dataReport[`${MWLoginID}_${MdmTp}`][
                                'respone_client_' + step
                            ]++;
                            glb_sv.dataReportOther
                                .respone_client_total++;
                            glb_sv.logDataReport();
                            glb_sv.logDataReportOther();
                            logger.error(
                                `SERVER_ERROR: step === 3 -> ws.once('finish') -> isWriteVideoDone`,
                                { error: err },
                            );
                            apiResponse.error(
                                res,
                                httpStatusCodes.INTERNAL_SERVER_ERROR,
                                locale.API_ALT_ERROR,
                            );
                        }
                    }
                });
            }
        } catch (error) {
            glb_sv
                .dataReport[`${MWLoginID}_${MdmTp}`]['request_server_error']++;
            glb_sv
                .dataReport[`${MWLoginID}_${MdmTp}`]['respone_client_' + step]++;
            glb_sv.dataReportOther.respone_client_total++;
            glb_sv.logDataReport();
            glb_sv.logDataReportOther();

            logger.error('SERVER_ERROR: try catch end', { error: error });
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.API_ALT_ERROR,
            );
        }
    });
};

function base64_encode(file: string): string {
    // read binary data
    var bitmap = fs.readFileSync(file);
    // convert binary data to base64 encoded string
    return Buffer.from(bitmap).toString('base64');
}

function getJson(url: string): any {
    const res = fs.readFileSync(url);
    try {
        return JSON.parse(res.toString());
    } catch {
        return null;
    }
}

const download: IController = async (req, res) => {
    // console.log("req.headers.authorization", req.headers)
    // @ts-expect-error
    const { seccode, fosId, target }: IEkycSchema['download'] =
        req.query;

    const pathUser = glb_sv.getPathUser(seccode, fosId);

    if (target === 'front_face') {
        const pathFile = `${pathUser}/video_selfie/image_selfie.jpg`;
        try {
            if (fs.existsSync(pathFile)) {
                res.download(pathFile);
            } else {
                apiResponse.error(
                    res,
                    httpStatusCodes.NOT_FOUND,
                    locale.FILE_NOT_FOUND,
                );
            }
        } catch (err) {
            console.error(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.FILE_NOT_FOUND,
            );
        }
    } else if (target === 'front_face_in_id') {
        const pathFile = `${pathUser}/video_selfie/image_selfie.jpg`;
        try {
            if (fs.existsSync(pathFile)) {
                res.download(pathFile);
            } else {
                apiResponse.error(
                    res,
                    httpStatusCodes.NOT_FOUND,
                    locale.FILE_NOT_FOUND,
                );
            }
        } catch (err) {
            console.error(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.FILE_NOT_FOUND,
            );
        }
    } else if (target === 'video') {
        // TODO: video_selfie.mp4 có 2 loại: *.mp4, *.mov
        const pathFile = `${pathUser}/video_selfie/video_selfie.mp4`;
        const pathFileMov = `${pathUser}/video_selfie/video_selfie.mov`;
        // console.log("path", { pathFile, pathFileMov }, fs.existsSync(pathFile));

        try {
            // Có file MP4
            const existMP4 = fs.existsSync(pathFile);
            const existMOV = fs.existsSync(pathFileMov);

            if (existMP4) {
                res.download(pathFile);
            } else if (existMOV) {
                res.download(pathFileMov);
            } else {
                apiResponse.error(
                    res,
                    httpStatusCodes.NOT_FOUND,
                    locale.FILE_NOT_FOUND,
                );
            }
        } catch (err) {
            console.error(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'err',
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.FILE_NOT_FOUND,
            );
        }
    } else if (target === 'front_id') {
        const pathFile = `${pathUser}/front_id/front_id.jpg`;
        try {
            if (fs.existsSync(pathFile)) {
                res.download(pathFile);
            } else {
                apiResponse.error(
                    res,
                    httpStatusCodes.NOT_FOUND,
                    locale.FILE_NOT_FOUND,
                );
            }
        } catch (err) {
            console.error(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.FILE_NOT_FOUND,
            );
        }
    } else if (target === 'back_id') {
        const pathFile = `${pathUser}/back_id/back_id.jpg`;
        try {
            if (fs.existsSync(pathFile)) {
                res.download(pathFile);
            } else {
                apiResponse.error(
                    res,
                    httpStatusCodes.NOT_FOUND,
                    locale.FILE_NOT_FOUND,
                );
            }
        } catch (err) {
            console.error(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.INTERNAL_SERVER_ERROR,
                locale.FILE_NOT_FOUND,
            );
        }
    } else {
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            locale.FILE_NOT_FOUND,
        );
    }
};

const addFaceToFPT: IController = async (req, res) => {
    // console.log("req.headers.authorization", req.headers)
    // @ts-expect-error
    const { seccode, fosId, unit }: IEkycSchema['addFaceToFPT'] =
        req.query;

    const pathUser = glb_sv.getPathUser(seccode, fosId);
    const pathFile = `${pathUser}/video_selfie/image_selfie.jpg`;

    const resAddFaceEkyc = await addFaceToFPTFaceSearch({
        fosid: fosId,
        seccode: seccode,
        pathImage: pathFile,
    });

    if (resAddFaceEkyc.code === '200') {
        apiResponse.result(res, resAddFaceEkyc, httpStatusCodes.OK);
    } else {
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            locale.API_ALT_ERROR,
            resAddFaceEkyc,
        );
    }
};
const searchFaceFPT: IController = async (req, res) => {
    // console.log("req.headers.authorization", req.headers)
    // @ts-expect-error
    const {
        seccode,
        fosId,
        unit,
        threshold,
    }: IEkycSchema['searchFaceFPT'] = req.query;

    const pathUser = glb_sv.getPathUser(seccode, fosId);
    const pathFile = `${pathUser}/video_selfie/image_selfie.jpg`;

    const resSearchFaceFPT = await searchFaceFPTFaceSearch({
        threshold: threshold,
        seccode: seccode,
        pathImage: pathFile,
        unit,
    });

    if (resSearchFaceFPT.code === '200') {
        apiResponse.result(res, resSearchFaceFPT, httpStatusCodes.OK);
    } else {
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            locale.API_ALT_ERROR,
            resSearchFaceFPT,
        );
    }
};

// Tạo user ngay lúc ekyc luôn. Add thì để sau khi duyệt
const createUserInFPTFaceSearch = async ({
    fosid,
    nameOfUser,
    seccode,
}: {
    fosid: string;
    nameOfUser: string;
    seccode: string;
}) => {
    try {
        const resEkyc = await insFptService.createUserInFPTFaceSearch(
            fosid,
            nameOfUser,
            createFaceSearchCollectionID(seccode),
        );
        return resEkyc;
    } catch (error) {
        logger.error('createUserInFPTFaceSearch', {
            fosid,
            nameOfUser,
            seccode,
            error,
        });
        return { codeALT: '209', messageErr: error };
    }
};

const addFaceToFPTFaceSearch = async ({
    fosid,
    seccode,
    pathImage,
}: {
    fosid: string;
    seccode: string;
    pathImage: string;
}) => {
    try {
        const resEkyc =
            await insFptService.addFaceToFPTFaceSearchCollection(
                fosid,
                createFaceSearchCollectionID(seccode),
                pathImage
            );
        return resEkyc;
    } catch (error) {
        logger.error('addFaceToFPTFaceSearch', {
            fosid,
            seccode,
            pathImage,
            error,
        });
        return { codeALT: '209', messageErr: error };
    }
};

const searchFaceFPTFaceSearch = async ({
    threshold,
    seccode,
    pathImage,
    unit,
}: {
    threshold: number;
    seccode: string;
    pathImage: string;
    unit: string;
}) => {
    try {
        const resEkyc = await insFptService.searchFaceInCollection(
            threshold,
            createFaceSearchCollectionID(seccode),
            pathImage,
            unit,
        );
        console.log(
            `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
            'searchFaceFPTFaceSearch',
            resEkyc,
        );
        return resEkyc;
    } catch (error) {
        logger.error('searchFaceFPTFaceSearch', {
            threshold,
            seccode,
            pathImage,
            unit,
            error,
        });
        return { codeALT: '209', messageErr: error };
    }
};

const uploadOther: IController = async (req, res) => {
    const form = new multiparty.Form();
    form.parse(req, async function (err, fields, files) {
        if (err) {
            console.error(err);
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
            return;
        }
        console.log(
            `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
            'ekyc upload other client request',
            JSON.stringify(fields),
            JSON.stringify(files),
        );

        if (!fields.userId || !fields.sec || !fields.step) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'REQUEST_ERROR userId, sec, step',
            );
            return;
        }
        let userId = fields.userId[0];
        if (!userId) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            return;
        }
        let SecCode = fields.sec[0];
        if (!glb_sv.LIST_SEC.includes(SecCode)) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'REQUEST_ERROR LIST_SEC',
            );
            return;
        }
        let fileName = fields.fileName ? fields.fileName[0] : '';
        let step = fields.step[0];
        if (!['1', '2', '3'].includes(step)) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'REQUEST_ERROR step',
            );
            return;
        }

        if (
            !files.file ||
            !files.file.length ||
            files.file.length > 1
        ) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'REQUEST_ERROR file',
            );
            return;
        }
        const file = files.file[0];
        if (!file || !file.path) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log(
                `${moment().format('DD/MM/YYYY HH:mm:ss')} `,
                'REQUEST_ERROR filePath',
            );
            return;
        }

        let fileMetadata: sharp.Metadata = {
            chromaSubsampling: '',
        };
        try {
            fileMetadata = await sharp(file.path).metadata();
        } catch (err) {
            console.log(
                `${moment().format(
                    'DD/MM/YYYY HH:mm:ss',
                )} REQUEST_ERROR ${err.toString()}`,
            );
        }

        if (fileMetadata.format == null) {
            console.log(
                `${moment().format(
                    'DD/MM/YYYY HH:mm:ss',
                )} REQUEST_ERROR file is not image`,
            );
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            return;
        }
        if (
            fileMetadata.format !== 'png' &&
            fileMetadata.format !== 'jpg' &&
            fileMetadata.format !== 'jpeg' &&
            fileMetadata.format !== 'webp'
        ) {
            console.log(
                `${moment().format(
                    'DD/MM/YYYY HH:mm:ss',
                )} REQUEST_ERROR file is not image: ${
                    fileMetadata.format
                }`,
            );
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            return;
        }

        let nameFile = fileName;
        if (step === '1') {
            nameFile = fileName
                ? `/${fileName}`
                : `/front_id. + ${fileMetadata.format}`;
        }
        if (step === '2') {
            nameFile = fileName
                ? `/${fileName}`
                : `/back_id. + ${fileMetadata.format}`;
        }
        if (step === '3') {
            nameFile = fileName
                ? `/${fileName}`
                : `/face_id. + ${fileMetadata.format}`;
        }
        const pathSec = path.join(
            process.cwd(),
            process.env.DB_LOCAL,
            'ekyc-other',
            SecCode,
        );
        const pathUser = path.join(pathSec, userId);
        if (!fs.existsSync(pathUser)) {
            fs.mkdirSync(pathUser);
        }
        const pathFileOrgin = path.join(pathUser, nameFile);
        fs.copyFile(file.path, pathFileOrgin, (err:any) => {
            if (err) {
                console.log(
                    `${moment().format(
                        'DD/MM/YYYY HH:mm:ss',
                    )} fs.copyFile error: ${err.toString()}`,
                );
                apiResponse.error(
                    res,
                    httpStatusCodes.INTERNAL_SERVER_ERROR,
                    locale.API_ALT_ERROR,
                );
                return;
            }

            apiResponse.result(res, {});
        });
    });
};

const informationUpload: IController = async (req:any, res:any) => { 
    try {
        const headers = req.headers;        
        if (!headers['x-update-info-x']
            || (headers['x-update-info-x']
            && headers['x-update-info-x'] !== hashSHA256)
        ) {
          apiResponse.error(
              res,
              httpStatusCodes.FORBIDDEN,
          );
          return
        }
        const form = new multiparty.Form();
        form.parse(req, async function (err, fields, files) {
            if (err) {
                logger.error(`SERVER_ERROR form.parse: ${err} ` + fields);
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.API_ALT_ERROR,
                );
                return; 
            }
           
            logger.info(`Request upload ` +  JSON.stringify(fields));
            if(!fields.type || !fields.fosNo || !fields.mdmTp){
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.API_ALT_ERROR,
                );
                return;
            }    
            if(['1','2'].includes(fields.type[0])){
                if(!files?.file || files?.file?.length  == 0){
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.API_ALT_ERROR,
                    );
                    return;
                }
            }

            if(['3'].includes(fields.type[0])){
                if(!files?.fileVideo || files?.fileVideo?.length  == 0 || !files?.fileSelfie || files?.fileSelfie?.length  == 0 ){
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.API_ALT_ERROR,
                    );
                    return;
                }
            }

            const param = {
                file:         fields.type[0] == '3' ?  null : files?.file[0],
                fileVideo:    fields.type[0] == '3' ?  files.fileVideo[0] : {},
                fileSelfie:   fields.type[0] == '3' ?  files.fileSelfie[0] : {},
                type:       fields.type[0],
                mdmTp:      fields.mdmTp[0],
                fosNo:      fields.fosNo[0],
            }
            const HOST_PATH = process.env.DIR_UPLOAD_IMAGE_UPD_INFO || 'temp-upload'
            const pathCommon = HOST_PATH + "/update-information"; 
            const pathCommonFOSNew = HOST_PATH + "/NewFOS";
        
            // pathCommon
            if (!fs.existsSync(pathCommon)) {
                fs.mkdirSync(pathCommon);
            }
            const pathUser = path.join(pathCommon, param.fosNo);
            if (!fs.existsSync(pathUser)) {
                fs.mkdirSync(pathUser);
            }
            const pathUserImageFront = path.join(pathUser,'card_front')
            if (!fs.existsSync(pathUserImageFront)) {
                fs.mkdirSync(pathUserImageFront);
            }
            const pathUserImageBack = path.join(pathUser,'card_back')
    
            if (!fs.existsSync(pathUserImageBack)) {
                fs.mkdirSync(pathUserImageBack);
            }
            const pathUserVideo = path.join(pathUser,'video_selfie')
            if (!fs.existsSync(pathUserVideo)) {
                fs.mkdirSync(pathUserVideo);
            }
            const pathUserSelfie = path.join(pathUser,'image_selfie')
            if (!fs.existsSync(pathUserSelfie)) {
                fs.mkdirSync(pathUserSelfie);
            }

            //pathCommonFOSNew
            if (!fs.existsSync(pathCommonFOSNew)) {
                fs.mkdirSync(pathCommonFOSNew);
            }
            // pathFront / Back Image

            // save file
            if(param.type == '1'){
                if(['png','jpeg','jpg'].includes(glb_sv.getTypeImage(param.file?.originalFilename)) ){
                    const buffer = fs.readFileSync(param.file?.path); // đọc từ file gốc
                    // Gọi API cũ
                    let filename_image_old = `${param.fosNo}_front.` + glb_sv.getTypeImage(param.file?.originalFilename)
                    let image_old = pathCommonFOSNew + `/${filename_image_old}`;
                    fs.writeFileSync(image_old, buffer);

                    // Gọi API mới
                    console.log('----- đúng hình mặt trước ------')
                    let filename_image = `${param.fosNo}_front.` + glb_sv.getTypeImage(param.file?.originalFilename)
                    let image = pathUserImageFront + `/${filename_image}`;
                    fs.writeFileSync(image, buffer);
                    // save
                    // gọi FPT kiểm tra mặt trước
                    try {
                        const FormData = require('form-data');
                        let option = makeOptionIdRecognition(fs.createReadStream(image))
                        const form = new FormData();
                        form.append('image',    option.formData.image)
                        form.append('face',     option.formData.face)
                        const config : any= {
                            method: option.method,
                            url:    option.url,
                            headers: {...option.headers,...form.getHeaders()},
                            data: form
                        }
                        const result = await axios.request(config)
                        // thêm mongodb
                        insert_log(result.data);                        
                        if([200, 201].includes(result.status)){
                            let data = result.data
                            logger.info('[Information] CHECK callIdRecognizie ================')
                            if(data &&  Array.isArray(data.data) && data.data.length > 0 &&  data.data[0].type !='old' ){
                                logger.info('[Information] START CHECK================')
                                const doe = data.data[0].doe ? data.data[0].doe :"31/12/2099" ;
                                let checkStrValid = /^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/.test(doe);
                                let check =  moment( doe,'DD/MM/YYYY').isValid() ;
                                logger.info(`[Information] callIdRecognizie  ${JSON.stringify({
                                    doe:  data.data[0].doe,
                                    checkStrValid,
                                    check
                                })}`);
                                if(!checkStrValid || !check){
                                    let _data = data.data.map((item1 :any ,index :number )=> {
                                        return index === 0 ? {...item1, doe: "31/12/2099"} : item1;
                                    })
                                    apiResponse.result(res, {
                                        path:{filename_image: filename_image},
                                        errorMessage: data.errorMessage,
                                        errorCode: data.errorCode,
                                        data: _data
                                    }    ); 
                                    return
                                }
                            }                   
                            fs.writeFileSync(pathUserImageFront +`/${param.fosNo}_front.json`, JSON.stringify(result.data));
                            // const resCheck = glb_sv.checkEkyc('1', '081', pathUserImageFront +`/${param.fosNo}_front.json`);
                            // console.log('resCheck: ', resCheck);
                            apiResponse.result(res, {...result.data, path:{filename_image: filename_image}}); 
                            return
                        }
                        fs.writeFileSync(pathUserImageFront +`/${param.fosNo}_front.json`, JSON.stringify(result.data));
                        apiResponse.result(res, {...result.data, path:{filename_image: filename_image}}); 
                        return
                    } catch (error) {
                        console.log('error: ', error?.response);
                        if(error?.response?.status != 200){ 
                            apiResponse.error(res, error.response.status || 500,  'API FPT: ' + (error.response?.data?.message ||  error?.response?.data?.errorMessage), null, {
                                errorCode:error.response?.data?.errorCode,
                                errorMessage:error.response?.data?.errorMessage,
                            });
                            fs.writeFileSync(pathUserImageFront +`/${param.fosNo}_front.json`, JSON.stringify(error.response?.data));
                            logger.error('[Information]ERROR API FPT callIdRecognizie ================ status: ' + error?.response.status)
                            return                     
                        }
                        apiResponse.error(res, httpStatusCodes.BAD_REQUEST, locale.API_ALT_ERROR)
                        console.log('error: ', error.response);
                        return
                        // logger.error(`${moment().format('DD/MM/YYYY HH:mm:ss')} `, 'callLiveness err retry: ' +" | "+ JSON.stringify(error.response ))
                    }
                }else{
                    console.log('----- sai hình mặt trước------')
                    // return { codeALT: '400', messageErr: 'Sai thông tin hình mặt trước' }
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Sai thông tin hình CCCD/ thẻ CC mặt trước')
                    return
                }
            }
            if(param.type == '2'){
                if(['png','jpeg','jpg'].includes(glb_sv.getTypeImage(param.file?.originalFilename)) ){
                    const buffer = fs.readFileSync(param.file?.path); // đọc từ file gốc

                    // Gọi API cũ
                    let filename_image_old = `${param.fosNo}_back.` + glb_sv.getTypeImage(param.file?.originalFilename)
                    let image_old = pathCommonFOSNew + `/${filename_image_old}`;
                    fs.writeFileSync(image_old, buffer);


                    console.log('----- đúng hình mặt sau ------')
                    let filename_image = `${param.fosNo}_back.` + glb_sv.getTypeImage(param.file?.originalFilename)
                    let image = pathUserImageBack + `/${filename_image}`;
                    // let image = pathUserImageBack +`/${param.fosNo}_back.` + glb_sv.getTypeImage(param.file?.originalFilename);
                    fs.writeFileSync(image, buffer);
                    try {
                        const FormData = require('form-data');
                        let option = makeOptionIdRecognition(fs.createReadStream(image))
                        const form = new FormData();
                        form.append('image',    option.formData.image)
                        form.append('face',     option.formData.face)
                        const config : any= {
                            method: option.method,
                            url:    option.url,
                            headers: {...option.headers,...form.getHeaders()},
                            data: form
                        }
                        const result = await axios.request(config)
                        // thêm mongodb
                        insert_log(result.data);
                        if([200, 201].includes(result.status)){
                            let data = result.data
                            logger.info('[Information][Back] CHECK callIdRecognizie ================')
                            if(data &&  Array.isArray(data.data) && data.data.length > 0 &&  data.data[0].type !='old' ){
                                logger.info('[Information][Back] START CHECK================')
                                const doe = data.data[0].doe ? data.data[0].doe :"31/12/2099" ;
                                let checkStrValid = /^(0?[1-9]|[12][0-9]|3[01])[\/\-](0?[1-9]|1[012])[\/\-]\d{4}$/.test(doe);
                                let check =  moment( doe,'DD/MM/YYYY').isValid() ;
                                logger.info(`[Information][Back] callIdRecognizie  ${JSON.stringify({
                                    doe:  data.data[0].doe,
                                    checkStrValid,
                                    check
                                })}`);
                                if(!checkStrValid || !check){
                                    let _data = data.data.map((item1 :any ,index :number )=> {
                                        return index === 0 ? {...item1, doe: "31/12/2099"} : item1;
                                    })
                                    let output = {
                                        path: {filename_image: filename_image},
                                        errorMessage: data.errorMessage,
                                        errorCode: data.errorCode,
                                        data: _data
                                    } 
                                    fs.writeFileSync(pathUserImageBack +`/${param.fosNo}_back.json`, JSON.stringify(output));
                                    apiResponse.result(res, output   ); 
                                    return
                                }
                            }
                            fs.writeFileSync(pathUserImageBack +`/${param.fosNo}_back.json`, JSON.stringify(result.data));
                            apiResponse.result(res, {...result.data, path:{filename_image: filename_image}}); 
                            return
                        }
                        fs.writeFileSync(pathUserImageBack +`/${param.fosNo}_back.json`, JSON.stringify(result.data));
                        // const resCheck = glb_sv.checkEkyc('2', '081', pathUserImageBack +`/${param.fosNo}_back.json`);
                        // console.log('resCheck: ', resCheck);

                        apiResponse.result(res, {...result.data, path: {filename_image: filename_image} }); 
                        return
                    } catch (error) {
                        console.log('error: ', error?.response);
                        if(error?.response?.status != 200){ 
                            apiResponse.error(res, error.response.status || 500,  'API FPT: ' + (error.response?.data?.message ||  error?.response?.data?.errorMessage), null, {
                                errorCode:error.response?.data?.errorCode,
                                errorMessage:error.response?.data?.errorMessage,
                            });
                            logger.error('[Information][Back] ERROR API FPT callIdRecognizie ================ status: ' + error?.response.status)
                            return                     
                        }
                        apiResponse.error(res, httpStatusCodes.BAD_REQUEST, locale.API_ALT_ERROR)
                        console.log('error: ', error.response);
                        return
                    }
                }else{
                    console.log('----- sai hình mặt sau------')
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Sai thông tin hình CCCD/ thẻ CC mặt sau')
                    return
                } 
            }
    
            if(param.type == '3'){
                let listCheck = {
                    image_selfie: '',
                    filename_image_selfie: '',
                    video_selfie: '',
                    filename_video_selfie: '',
                }
                // thực hiện lưu ảnh 
                if(['png','jpeg','jpg'].includes(glb_sv.getTypeImage(param.fileSelfie?.originalFilename)) ){
                    const buffer = fs.readFileSync(param.fileSelfie?.path); // đọc từ file gốc
                    // Gọi API cũ
                    let filename_image_old = `${param.fosNo}_image_selfie.` + glb_sv.getTypeImage(param.fileSelfie?.originalFilename)
                    let image_old = pathCommonFOSNew + `/${filename_image_old}`;
                    fs.writeFileSync(image_old, buffer);
                    // image selfie hợp lệ
                    console.log('----- đúng image selfie ------');
                    // let path_image = pathUserSelfie +`/${param.fosNo}_image_selfie.` + glb_sv.getTypeImage(param.fileSelfie?.originalFilename);
                    let filename_image = `${param.fosNo}_image_selfie.` + glb_sv.getTypeImage(param.fileSelfie?.originalFilename)
                    let path_image = pathUserSelfie + `/${filename_image}`;
                    fs.writeFileSync(path_image, buffer);
                    listCheck.image_selfie = path_image;
                    listCheck.filename_image_selfie = filename_image
                }else{
                    console.log('----- sai image selfie ------')
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Sai thông tin image selfie');
                    return
                }        
 
                // thực hiện lưu video
                if(['mp4','mov'].includes(glb_sv.getTypeImage(param.fileVideo?.originalFilename)) ){
                    const buffer = fs.readFileSync(param.fileVideo?.path); // đọc từ file gốc
                    // Gọi API cũ
                    let filename_image_old = `${param.fosNo}_video_selfie.` + glb_sv.getTypeImage(param.fileVideo?.originalFilename)
                    let image_old = pathCommonFOSNew + `/${filename_image_old}`;
                    fs.writeFileSync(image_old, buffer);

                    console.log('----- đúng video ------')
                    // let path_video = pathUserSelfie +`/${param.fosNo}_video_selfie.` + glb_sv.getTypeImage(param.fileVideo?.originalFilename);
                    let filename_video = `${param.fosNo}_video_selfie.` + glb_sv.getTypeImage(param.fileVideo?.originalFilename)
                    let path_video = pathUserSelfie + `/${filename_video}`;
                    fs.writeFileSync(path_video, buffer);
                    listCheck.video_selfie = path_video;
                    listCheck.filename_video_selfie = filename_video
                }else{
                    console.log('----- sai video ------')
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Sai thông tin video selfie');
                    return
                }

                /// thực hiện gọi API kiểm tra
                let pathUserCardFront = pathUserImageFront + `/${param.fosNo}_front.` + glb_sv.getTypeImage(param.fileSelfie?.originalFilename);
                let pathUserCardBack  = pathUserImageBack  + `/${param.fosNo}_back.`  + glb_sv.getTypeImage(param.fileSelfie?.originalFilename);
                if(!fs.existsSync(pathUserCardFront) || !fs.existsSync(pathUserCardBack)){
                    console.log('----- Không tồn tại ------')
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Vui lòng Upload CCCD/ Thẻ Căn cước.');
                    return
                }
                // gọi API FPT 
                console.log('pathUserCardFront: ', pathUserCardFront);
                const FormData = require('form-data');
                const option:any = new FormData();
                option.append('file[]', fs.createReadStream(pathUserCardFront));
                option.append('file[]', fs.createReadStream(listCheck.image_selfie));
                option.append('validate', '1');
                const config: any = {
                    method: 'post',
                    url: 'https://api.fpt.ai/dmp/checkface/v1',
                    headers: {
                        "Content-Type": "multipart/form-data",
                        "api_key": process.env.FPT_API_KEY,
                        ...option.getHeaders()
                    },
                    data: option
                };
                try {
                    const result = await axios.request(config)
                    if(result.status == 200){
                        console.log('result: ', result.data);
                        // const resCheck = glb_sv.checkEkyc('3', '081', pathUserSelfie +`/${param.fosNo}_selfie.json`, true);
                        // console.log('resCheck: ', resCheck);
                        fs.writeFileSync(pathUserSelfie +`/${param.fosNo}_selfie.json`, JSON.stringify(result.data));
                        apiResponse.result(res,{...result.data, 
                            path: {
                                filename_image: listCheck.filename_image_selfie,
                                filename_video: listCheck.filename_video_selfie,
                            }
                        }); 
                        return
                    }        
                } catch (error) {
                    console.log('error: ', error);
                    apiResponse.error(res, httpStatusCodes.BAD_REQUEST, '[ERROR]CALL API FPT'); 
                    return
                }                     
            }

            apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Thực hiện lỗi');
            return
        });
    } catch (error) {
        console.log('error: ', error);
        apiResponse.error(res, httpStatusCodes.INTERNAL_SERVER_ERROR);
        return
    }
}
const makeOptionIdRecognition = (image: any) => {
    const option = {
        method: "POST",
        url: process.env.FPT_DOMAIN_ID_RECOGNITION,
        port: 443,
        headers: {
            "Content-Type": "multipart/form-data",
            "api_key": process.env.FPT_API_KEY
        },
        formData: {
            "image": image,
            "face": "1"     // cho phép cắt hình từ image
        }
    }
    return option
}

const bosUpload: IController = async (req:any, res:any) => {
    try {
        const {} = req.body
        const {} = req.headers
        const headers = req.headers;        
        if (!headers['x-bos-upload-x']
            || (headers['x-bos-upload-x']
            && headers['x-bos-upload-x'] !== hashSHA256)
        ) {
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
            return
        }
        const form = new multiparty.Form(); 
        form.parse(req, async function (err, fields, files) {
            if (err) {
                logger.error(`SERVER_ERROR form.parse: ${err} ` + fields);
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                return; 
            }
            logger.info(`Request upload ` +  JSON.stringify(fields));
            if(!fields.cus_no || !fields.image_type || !fields.image_format || !fields.tab_name){
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                return;
            }

            if(['1','2','3'].includes(fields.image_type[0])){
                if(!files?.file || files?.file?.length  == 0){
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.FILE_NOT_FOUND,
                    );
                    return;
                }
                const fileSizeInBytes = files?.file[0].size;
                const maxSize = 2 * 1024 * 1024; // 2MB

                if (fileSizeInBytes > maxSize) {
                     apiResponse.error(
                        res,
                        httpStatusCodes.REQUEST_TOO_LONG,
                        'File too large. Max 2MB allowed.',
                    );
                    return
                }
            } 

            if(fields.tab_name[0].trim().length == 0){
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                return;
            } 
            const param = {
                file:               files?.file[0],
                image_type:         fields.image_type[0],
                cus_no:             fields.cus_no[0],
                image_format:       fields.image_format[0],
                tab_name:           fields.tab_name[0],
                timestemp:          moment().format('YYYYMMDD')
            }

            // kiểm tra file 
            if(!['png','jpeg','jpg'].includes(glb_sv.getTypeImage(param.file?.originalFilename)?.toLowerCase()) ){ 
                logger.info(`Request upload ` +  3);
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.REQUEST_ERROR,
                );
                return;
            }

            const HOST_PATH = process.env.DIR_UPLOAD_IMAGE_UPD_INFO || 'temp-upload'
            const pathCommon = HOST_PATH + ""; 
            // pathCommon
            if (!fs.existsSync(pathCommon)) {
                fs.mkdirSync(pathCommon);
            }

            // const pathUser = path.join(pathCommon, param.cus_no);
            // if (!fs.existsSync(pathUser)) {
            //     fs.mkdirSync(pathUser);
            // }
            // thực hiện lưu ảnh
            let filename_image, image;
            const buffer = fs.readFileSync(param.file?.path); // đọc từ file gốc
            if(param.image_type == 1){
                filename_image = `3205_${param.tab_name}_${param.cus_no}_${param.timestemp}_front.` + glb_sv.getTypeImage(param.file?.originalFilename)
                image = pathCommon + `/${filename_image}`;

            }else if(param.image_type == 2){
                filename_image = `3205_${param.tab_name}_${param.cus_no}_${param.timestemp}_back.` + glb_sv.getTypeImage(param.file?.originalFilename)
                image = pathCommon + `/${filename_image}`;

            }else if(param.image_type == 3){
                filename_image = `3205_${param.tab_name}_${param.cus_no}_${param.timestemp}_portrait.` + glb_sv.getTypeImage(param.file?.originalFilename)
                image = pathCommon + `/${filename_image}`;
            }
            
            // kiểm tra thành công không
            if(filename_image && image){
                fs.writeFileSync(image, buffer);
                apiResponse.result(res,{
                    message: 'Thực hiện thành công.',
                    data: {
                        filename_image: filename_image
                    }
                }); 
                return
            }else{
                apiResponse.error(res, httpStatusCodes.BAD_REQUEST, 'Lỗi: Không thể lưu ảnh được.');
                return
            }            
            // thực hiện 
        })

    }catch(error){
        console.log('error: ', error);
        apiResponse.error(res, httpStatusCodes.INTERNAL_SERVER_ERROR);
        return
    }
}

const copyBos: IController = async (req:any, res:any) => {
    try {
        const { cust_no, file_name, file_type, update_type, update_date, update_time, file_rename } = req.body;
        const {} = req.headers
        const headers = req.headers;        
        if (!headers['x-bos-copy-x']
            || (headers['x-bos-copy-x']
                && headers['x-bos-copy-x'] !== hashSHA256)) {
            logger.info('[Error]: Step 1  Missing or invalid fields in request headers');
             apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
            return;
        }
        if (!cust_no || !file_name || !file_type || !update_type || !update_date || !update_time || !file_rename) {
            logger.info('[Error]: Step 2  Missing or invalid fields in request body');
            apiResponse.error(res, httpStatusCodes.BAD_REQUEST, locale.REQUEST_ERROR);
            return;
        }
        // kiểm tra file 
        if (!['png', 'jpeg', 'jpg'].includes(file_type.toLowerCase())) {
            logger.info('[Error]: Step 3 file type invalid');
            apiResponse.error(res, httpStatusCodes.BAD_REQUEST, locale.REQUEST_ERROR);
            return;
        }
        // thực hiện kiểm tra folder
        const HOST_PATH = process.env.DIR_BOS || 'temp-upload';
        const HOST_PATH_BK = process.env.DIR_BOS_BK || 'temp-upload/R12288_BAK';
        const pathCommon = HOST_PATH;
        const pathCommonBK = HOST_PATH_BK;
        // kiểm tra file tồn tại
        const pathFile = pathCommon + '/' + file_name;
        if (!fs.existsSync(pathFile)) {
            logger.info('[Error]: Step 3  File not found');
            apiResponse.error(res, httpStatusCodes.BAD_REQUEST, locale.FILE_NOT_FOUND);
            return;
        }
        // nơi chứa ảnh
        // pathCommonBK
        if (!fs.existsSync(pathCommonBK)) {
            fs.mkdirSync(pathCommonBK);
        }
        // Lấy file hiện có (app upload) chuyển vào backup 
        // Đổi tên file được bos upload thành file của app upload
        // file APP
        const source = pathCommon + '/' + file_name;
        // file Bos
        const sourceRename = pathCommon + '/' + file_rename;
        const file_name_bk = update_type + '_' + cust_no + '_' + update_date + '_' + update_time + '.' + file_type;
        const destination = pathCommonBK + '/' + file_name_bk;
        logger.info(`[COPY] source: ${source} | destination: ${destination}`);
        // thực hiện copy
        fs.copyFileSync(source, destination);
        logger.info(`[COPY SUCCESS] Moved to: ${destination}`);
        // thực hiện rename file source
        logger.info(`[RENAME] source: ${source} | destination: ${sourceRename}`);
        fs.renameSync(sourceRename, source);
        logger.info(`[RENAME SUCCESS]: ${sourceRename}`);
        apiResponse.result(res, {
            success: true,
            message: "Thực hiện thành công",
            data: {
                file_name_bk: file_name_bk
            }
        });
        return;
    } catch (error) {
        console.log('error: ', error);
        apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            locale.API_ALT_ERROR,
        );
        return
    }
}
export default {
    checkStep,
    upload,
    download,
    addFaceToFPT,
    searchFaceFPT,
    uploadOther,
    informationUpload,
    bosUpload,
    copyBos,
};
