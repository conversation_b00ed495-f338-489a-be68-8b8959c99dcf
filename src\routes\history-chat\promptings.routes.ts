import { Router } from 'express';
import {
    // CRUD controllers
    createPrompting,
    updatePrompting,
    deletePrompting,
    getPromptings,
    getPromptingById,
    
    // Specialized controllers
    getPromptingsByLanguage,
    getPromptingsByCodeType,
    getPromptingsByStatus,
    
} from '../../controllers/promptings.controller';

import {
    // Validation middleware
    validateCreatePrompting,
    validateUpdatePrompting,
    validatePagination,
    validatePathParams,
    validateFilterParams,
} from '../../middleware/promptings.validation';

const router = Router();

// ============ Basic CRUD Routes ============

/**
 * @route POST /api/promptings
 * @desc Create new prompting
 * @body {PromptingsModel} - Prompting data
 */
router.post('/', validateCreatePrompting, createPrompting);

/**
 * @route PUT /api/promptings/:id
 * @desc Update prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 * @body {Partial<PromptingsModel>} - Update data
 */
router.put('/:id', validateUpdatePrompting, updatePrompting);

/**
 * @route DELETE /api/promptings/:id
 * @desc Delete prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 */
router.delete('/:id', validatePathParams, deletePrompting);

/**
 * @route GET /api/promptings
 * @desc Get all promptings with pagination and filtering
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 * @query {string} LNG_TP - Filter by language type
 * @query {string} COL_CD - Filter by column code
 * @query {string} COL_CD_TP - Filter by column code type
 * @query {string} COL_CD_TP_NM - Filter by column code type name
 * @query {number} ACTIVE_YN - Filter by active status (0 | 1)
 */
router.get('/', validatePagination, validateFilterParams, getPromptings);

/**
 * @route GET /api/promptings/:id
 * @desc Get prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 */
router.get('/:id', validatePathParams, getPromptingById);

// ============ Specialized Query Routes ============

// /**
//  * @route GET /api/promptings/language/:lng_tp
//  * @desc Get promptings by language type
//  * @params {string} lng_tp - Language type
//  * @query {number} page - Page number (default: 1)
//  * @query {number} limit - Items per page (default: 10, max: 100)
//  * @query {string} sort - Sort field (default: '_id')
//  * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
//  */
// router.get('/language/:lng_tp', validatePathParams, validatePagination, getPromptingsByLanguage);

// /**
//  * @route GET /api/promptings/code-type/:col_cd_tp
//  * @desc Get promptings by column code type
//  * @params {string} col_cd_tp - Column code type
//  * @query {number} page - Page number (default: 1)
//  * @query {number} limit - Items per page (default: 10, max: 100)
//  * @query {string} sort - Sort field (default: '_id')
//  * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
//  */
// router.get('/code-type/:col_cd_tp', validatePathParams, validatePagination, getPromptingsByCodeType);

// /**
//  * @route GET /api/promptings/status/:status
//  * @desc Get promptings by status (active/inactive)
//  * @params {string} status - Status ('active' | 'inactive')
//  * @query {number} page - Page number (default: 1)
//  * @query {number} limit - Items per page (default: 10, max: 100)
//  * @query {string} sort - Sort field (default: '_id')
//  * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
//  */
// router.get('/status/:status', validatePathParams, validatePagination, getPromptingsByStatus);


export default router;