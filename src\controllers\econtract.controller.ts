import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insFptEcontractService from '../services/EcontractService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';

const callGetSession: IController = async (req, res) => {
  const resEcontract = await insFptEcontractService.getSession(req) ;
  try {
    apiResponse.result(res, { session: 'ok', response: resEcontract });
  } catch (e) {
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }

}

const callGetNewCookie: IController = async (req, res) => {
  let {contactId, envId} = req.body;

  if(!contactId || !envId) {
    return  apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.REQUEST_ERROR,
    );

  } else {
    try {
      const resEcontract = await insFptEcontractService.getNewCookie(req);

      console.log(resEcontract);

      apiResponse.result(res, { response: resEcontract });
    } catch (e) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          locale.API_ALT_ERROR,
      );
    }
  }

}

const callGetAllTemplate: IController = async (req, res) => {
  let {update} = req.body;

  if(!update ) {
    return  apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.REQUEST_ERROR,
    );

  } else {
    try {
      const resEcontract = await insFptEcontractService.getAllTemplate(req);

      console.log(resEcontract);

      apiResponse.result(res, { response: resEcontract });
    } catch (e) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          locale.API_ALT_ERROR,
      );
    }
  }

}

const callExtendDateExpire: IController = async (req, res) => {
  let {extendDate, envID} = req.body;

  if(!extendDate ||  !envID) {
    return  apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.REQUEST_ERROR,
    );

  } else {
    try {
      const resEcontract = await insFptEcontractService.extendDateExpire(extendDate, envID);

      console.log(resEcontract);

      apiResponse.result(res, { response: resEcontract });
    } catch (e) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          locale.API_ALT_ERROR,
      );
    }
  }

}

const callCheckKeyLinkExit: IController = async (req, res) => {
  let {refId} = req.body;

  if(!refId ||  !refId) {
    return  apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.REQUEST_ERROR,
    );

  } else {
    try {
      const resEcontract = await insFptEcontractService.checkKeyLink(refId);

      console.log(resEcontract);

      apiResponse.result(res, { response: resEcontract });
    } catch (e) {
      apiResponse.error(
          res,
          httpStatusCodes.BAD_REQUEST,
          locale.API_ALT_ERROR,
      );
    }
  }

}

export default {
  callGetSession,
  callGetNewCookie,
  callGetAllTemplate,
  callExtendDateExpire,
  callCheckKeyLinkExit
}