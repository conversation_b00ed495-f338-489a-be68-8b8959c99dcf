import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import insBadWordService from '../services/BadwordFilterService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';

const hashSHA256 = process.env.BADWORD_HEADER || 'e3b07158b255f690faff30641b3d9df85b2c1e663cf92bd634c802ec70b05783' // keyforcheckbadword
const checkBadWord: IController = async (req, res) => {
  const { headers } = req;
  if (!headers['x-bad-word-x']
      || (headers['x-bad-word-x']
          && headers['x-bad-word-x'] !== hashSHA256)
  ) {
    apiResponse.error(
        res,
        httpStatusCodes.FORBIDDEN,
    );
    return
  }

  try {
    const result = await insBadWordService.checkBadWord(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );

  }
  return apiResponse.result(res, { response: 'OK' });
}

const addBadWord: IController = async (req, res) => {
  const { headers } = req;
  if (!headers['x-bad-word-x']
      || (headers['x-bad-word-x']
          && headers['x-bad-word-x'] !== hashSHA256)
  ) {
    apiResponse.error(
        res,
        httpStatusCodes.FORBIDDEN,
    );
    return
  }

  try {
    const result = await insBadWordService.addBadWord(req);
    apiResponse.result(res, result);
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
  return apiResponse.result(res, { response: 'OK' });
}

export default {
  checkBadWord,
  addBadWord,
}
