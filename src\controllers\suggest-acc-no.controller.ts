import IController from 'IController';
import apiResponse from '../utilities/apiResponse';
import logger from '../config/logger';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import insSuggestAccNoService from '../services/SuggestAccNoService';

const getSuggestList: IController = async (req, res): Promise<void> => {
  try {
    const result = await insSuggestAccNoService.getSuggestAccountList(req)
    if(result.codeALT == 209 || result.messageErr){
      apiResponse.error(
        res,
        httpStatusCodes.INTERNAL_SERVER_ERROR,
        locale.API_ALT_ERROR,
      );
      return
    }
    apiResponse.result(res, {...result });
    return
  } catch (error) {
    console.log('getSuggestList error: ', error);
    logger.error('getSuggestList', {
      error,
    });
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
}

const checkTempExist: IController = async (req, res): Promise<void> => {
  try {
    const result = await insSuggestAccNoService.checkTempExist(req)
    if(result.codeALT == 209 || result.messageErr){
      apiResponse.error(
        res,
        httpStatusCodes.INTERNAL_SERVER_ERROR,
        locale.API_ALT_ERROR,
      );
      return
    }
    apiResponse.result(res, result);
    return
  } catch (error) {
    console.log('checkTempExist error: ', error);
    logger.error('checkTempExist', {
      error,
    });
    apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        locale.API_ALT_ERROR,
    );
  }
}

export default {
  getSuggestList,
  checkTempExist,
}
