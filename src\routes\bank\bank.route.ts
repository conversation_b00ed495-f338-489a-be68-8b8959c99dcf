import express from 'express';
import bankController from '../../controllers/bank.controller';
const router = express.Router();

router.post(
    '/getImage',
    bankController.callGetImage
);

router.post(
    '/updateStatusAccBank',
    bankController.callUpdateStatusAcc
);

router.post(
    '/callCreateFileZip',
    bankController.callCreateFileZip
);

router.post(
    '/callGetFileZip',
    bankController.callGetFileZip
);



export default router;