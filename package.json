{"name": "fos-filesgateway", "version": "1.0.1", "description": "make by <PERSON><PERSON>ee.", "main": "index.js", "scripts": {"postinstall": "patch-package", "test": "export ENV=dev && node Source/index.js", "product": "export ENV=pro && nodemon Source/index.js", "prebuild": "rimraf dist/", "build": "tsc", "start": "yarn serve", "serve": "export ENV=dev && node dist/index.js", "watch-node": "nodemon", "compile": "export ENV=dev && tsc && node dist/index.js", "dev": "nodemon -e ts  --exec \"yarn compile\"", "compile-pro": "export ENV=pro && tsc && node dist/index.js", "pro": "nodemon -e ts  --exec \"yarn compile-pro\"", "devwin": "cross-env ENV=dev node dist/index.js"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.3.4", "@types/archiver": "^6.0.3", "@types/cors": "^2.8.12", "@types/express": "4.17.1", "@types/jest": "^26.0.4", "@types/joi": "17.2.3", "@types/lodash": "^4.14.182", "@types/mongoose": "^5.11.97", "@types/multiparty": "^0.0.33", "@types/node-cron": "^3.0.11", "@types/rimraf": "^3.0.2", "@types/supertest": "^2.0.10", "@types/uuid": "^8.3.4", "cross-env": "^7.0.3", "jest": "^26.1.0", "nodemon": "^2.0.16", "patch-package": "^6.5.1", "rimraf": "^3.0.2", "supertest": "6.1.3", "ts-jest": "^26.1.3", "ts-node": "10.0.0", "tslint": "^5.20.1", "tslint-config-airbnb": "^5.11.1"}, "dependencies": {"@img/sharp-win32-x64": "^0.34.3", "archiver": "^7.0.1", "axios": "^0.26.1", "bcrypt": "^5.0.0", "celebrate": "13.0.3", "cors": "^2.8.5", "dotenv": "^16.0.1", "express": "^4.18.1", "express-jwt": "^7.7.5", "express-winston": "4.1.0", "form-data": "^4.0.0", "http-status-codes": "2.1.4", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "moment": "^2.29.3", "mongoose": "^6.13.0", "morgan": "^1.9.1", "multiparty": "^4.2.3", "node-cron": "^4.2.0", "reflect-metadata": "^0.1.13", "request": "^2.88.2", "sharp": "^0.34.3", "typescript": "^4.7.4", "uuid": "^8.3.2", "winston": "^3.2.1", "xlsx": "^0.18.5"}}