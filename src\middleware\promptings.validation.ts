import { Request, Response, NextFunction } from 'express';
import { PromptingsModel } from '../database/dto/PromptingsModel';

// Utility function để check valid string
const isValidString = (value: any): boolean => {
    return typeof value === 'string' && value.trim().length > 0;
};

// Utility function để check valid number
const isValidNumber = (value: any): boolean => {
    return typeof value === 'number' && !isNaN(value);
};

// Utility function để check valid ObjectId format
const isValidObjectId = (value: any): boolean => {
    return typeof value === 'string' && /^[0-9a-fA-F]{24}$/.test(value);
};

// Validation cho tạo PromptingsModel
export const validateCreatePrompting = (req: Request, res: Response, next: NextFunction) => {
    const { LNG_TP, COL_CD, COL_CD_TP, COL_CD_TP_NM, ACTIVE_YN }: PromptingsModel = req.body;
    
    const errors: string[] = [];
    
    if (!isValidString(LNG_TP)) {
        errors.push('LNG_TP is required and must be a valid string');
    }
    
    if (!isValidString(COL_CD)) {
        errors.push('COL_CD is required and must be a valid string');
    }
    
    if (!isValidString(COL_CD_TP)) {
        errors.push('COL_CD_TP is required and must be a valid string');
    }
    
    if (!isValidString(COL_CD_TP_NM)) {
        errors.push('COL_CD_TP_NM is required and must be a valid string');
    }
    
    if (!isValidNumber(ACTIVE_YN) || (ACTIVE_YN !== 0 && ACTIVE_YN !== 1)) {
        errors.push('ACTIVE_YN is required and must be 0 or 1');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho update PromptingsModel
export const validateUpdatePrompting = (req: Request, res: Response, next: NextFunction) => {
    const { id } = req.params;
    const updateData = req.body;
    
    const errors: string[] = [];
    
    if (!isValidObjectId(id)) {
        errors.push('ID parameter must be a valid ObjectId');
    }
    
    if (Object.keys(updateData).length === 0) {
        errors.push('Update data cannot be empty');
    }
    
    // Validate specific fields if they exist in update data
    if (updateData.LNG_TP !== undefined && !isValidString(updateData.LNG_TP)) {
        errors.push('LNG_TP must be a valid string if provided');
    }
    
    if (updateData.COL_CD !== undefined && !isValidString(updateData.COL_CD)) {
        errors.push('COL_CD must be a valid string if provided');
    }
    
    if (updateData.COL_CD_TP !== undefined && !isValidString(updateData.COL_CD_TP)) {
        errors.push('COL_CD_TP must be a valid string if provided');
    }
    
    if (updateData.COL_CD_TP_NM !== undefined && !isValidString(updateData.COL_CD_TP_NM)) {
        errors.push('COL_CD_TP_NM must be a valid string if provided');
    }
    
    if (updateData.ACTIVE_YN !== undefined && (!isValidNumber(updateData.ACTIVE_YN) || (updateData.ACTIVE_YN !== 0 && updateData.ACTIVE_YN !== 1))) {
        errors.push('ACTIVE_YN must be 0 or 1 if provided');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho pagination parameters
export const validatePagination = (req: Request, res: Response, next: NextFunction) => {
    const { page, limit, sort, sortOrder } = req.query;
    
    const errors: string[] = [];
    
    if (page && (isNaN(Number(page)) || Number(page) < 1)) {
        errors.push('page must be a positive number');
    }
    
    if (limit && (isNaN(Number(limit)) || Number(limit) < 1 || Number(limit) > 100)) {
        errors.push('limit must be a positive number and not exceed 100');
    }
    
    // Validate sort parameter
    if (sort && typeof sort !== 'string') {
        errors.push('sort must be a string');
    }
    
    // Validate sortOrder parameter
    if (sortOrder && !['asc', 'desc'].includes(sortOrder as string)) {
        errors.push('sortOrder must be either "asc" or "desc"');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho path parameters
export const validatePathParams = (req: Request, res: Response, next: NextFunction) => {
    const { id, lng_tp, col_cd_tp, status } = req.params;
    
    const errors: string[] = [];
    
    if (id && !isValidObjectId(id)) {
        errors.push('ID parameter must be a valid ObjectId');
    }
    
    if (lng_tp && !isValidString(lng_tp)) {
        errors.push('lng_tp parameter must be a valid string');
    }
    
    if (col_cd_tp && !isValidString(col_cd_tp)) {
        errors.push('col_cd_tp parameter must be a valid string');
    }
    
    if (status && !['active', 'inactive'].includes(status)) {
        errors.push('status parameter must be "active" or "inactive"');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho bulk operations
export const validateBulkOperation = (req: Request, res: Response, next: NextFunction) => {
    const { ids, updateData } = req.body;
    const { operation } = req.params; // 'delete' hoặc 'update'
    
    const errors: string[] = [];
    
    if (!Array.isArray(ids) || ids.length === 0) {
        errors.push('ids must be a non-empty array');
    }
    
    if (Array.isArray(ids)) {
        const invalidIds = ids.filter(id => !isValidObjectId(id));
        if (invalidIds.length > 0) {
            errors.push('All ids must be valid ObjectIds');
        }
    }
    
    // For update operations, validate updateData
    if (operation === 'update') {
        if (!updateData || typeof updateData !== 'object') {
            errors.push('updateData is required for update operations and must be an object');
        } else {
            // Validate update fields
            if (updateData.LNG_TP !== undefined && !isValidString(updateData.LNG_TP)) {
                errors.push('LNG_TP must be a valid string if provided in updateData');
            }
            
            if (updateData.COL_CD !== undefined && !isValidString(updateData.COL_CD)) {
                errors.push('COL_CD must be a valid string if provided in updateData');
            }
            
            if (updateData.COL_CD_TP !== undefined && !isValidString(updateData.COL_CD_TP)) {
                errors.push('COL_CD_TP must be a valid string if provided in updateData');
            }
            
            if (updateData.COL_CD_TP_NM !== undefined && !isValidString(updateData.COL_CD_TP_NM)) {
                errors.push('COL_CD_TP_NM must be a valid string if provided in updateData');
            }
            
            if (updateData.ACTIVE_YN !== undefined && (!isValidNumber(updateData.ACTIVE_YN) || (updateData.ACTIVE_YN !== 0 && updateData.ACTIVE_YN !== 1))) {
                errors.push('ACTIVE_YN must be 0 or 1 if provided in updateData');
            }
        }
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho filter parameters
export const validateFilterParams = (req: Request, res: Response, next: NextFunction) => {
    const { LNG_TP, COL_CD, COL_CD_TP, COL_CD_TP_NM, ACTIVE_YN } = req.query;
    
    const errors: string[] = [];
    
    if (LNG_TP && !isValidString(LNG_TP)) {
        errors.push('LNG_TP filter must be a valid string');
    }
    
    if (COL_CD && !isValidString(COL_CD)) {
        errors.push('COL_CD filter must be a valid string');
    }
    
    if (COL_CD_TP && !isValidString(COL_CD_TP)) {
        errors.push('COL_CD_TP filter must be a valid string');
    }
    
    if (COL_CD_TP_NM && !isValidString(COL_CD_TP_NM)) {
        errors.push('COL_CD_TP_NM filter must be a valid string');
    }
    
    if (ACTIVE_YN && (!isValidNumber(Number(ACTIVE_YN)) || (Number(ACTIVE_YN) !== 0 && Number(ACTIVE_YN) !== 1))) {
        errors.push('ACTIVE_YN filter must be 0 or 1');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};

// Validation cho search parameters
export const validateSearchParams = (req: Request, res: Response, next: NextFunction) => {
    const { search, searchField } = req.query;
    
    const errors: string[] = [];
    
    if (search && typeof search !== 'string') {
        errors.push('search must be a string');
    }
    
    if (searchField && !['LNG_TP', 'COL_CD', 'COL_CD_TP', 'COL_CD_TP_NM'].includes(searchField as string)) {
        errors.push('searchField must be one of: LNG_TP, COL_CD, COL_CD_TP, COL_CD_TP_NM');
    }
    
    if (errors.length > 0) {
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors
        });
    }
    
    next();
};
