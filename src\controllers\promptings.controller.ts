import { Request, Response } from 'express';
import promptingsService from '../database/services/promptings.service';
import { PromptingsModel } from '../database/dto/PromptingsModel';

// ============ PromptingsModel Controllers ============

// Tạo prompting mới
export const createPrompting = async (req: Request, res: Response) => {
    try {
        const promptingData: PromptingsModel = req.body;
        const result = await promptingsService.createPrompting(promptingData);
        
        if (result.success) {
            res.status(201).json({
                success: true,
                message: 'Prompting created successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Cập nhật prompting
export const updatePrompting = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        const updateData: Partial<PromptingsModel> = req.body;
        
        const result = await promptingsService.updatePrompting(id, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Prompting updated successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Xóa prompting
export const deletePrompting = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        
        const result = await promptingsService.deletePrompting(id);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Prompting deleted successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy danh sách promptings với filter và phân trang
export const getPromptings = async (req: Request, res: Response) => {
    try {
        const { 
            page = 1, 
            limit = 10, 
            sort = '_id', 
            sortOrder = 'desc',
            ...filter 
        } = req.query;
        
        const filterData = {
            filter: filter,
            page: parseInt(page as string),
            limit: Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort: sort as string,
            sortOrder: (sortOrder as string) === 'asc' ? 'asc' as const : 'desc' as const
        };
        
        const result = await promptingsService.getPromptings(filterData);
        
        res.status(200).json({
            success: true,
            message: 'Promptings retrieved successfully',
            data: result.data
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy prompting theo ID
export const getPromptingById = async (req: Request, res: Response) => {
    try {
        const { id } = req.params;
        
        const result = await promptingsService.getPromptingById(id);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Prompting retrieved successfully',
                data: result.data
            });
        } else {
            res.status(404).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy promptings theo language type
export const getPromptingsByLanguage = async (req: Request, res: Response) => {
    try {
        const { lng_tp } = req.params;
        const { 
            page = 1, 
            limit = 10, 
            sort = '_id', 
            sortOrder = 'desc' 
        } = req.query;
        
        const result = await promptingsService.getPromptingsByLanguage(
            lng_tp, 
            parseInt(page as string), 
            Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort as string,
            (sortOrder as string) === 'asc' ? 'asc' : 'desc'
        );
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Promptings retrieved successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy promptings theo code type
export const getPromptingsByCodeType = async (req: Request, res: Response) => {
    try {
        const { col_cd_tp } = req.params;
        const { 
            page = 1, 
            limit = 10, 
            sort = '_id', 
            sortOrder = 'desc' 
        } = req.query;
        
        const result = await promptingsService.getPromptingsByCodeType(
            col_cd_tp, 
            parseInt(page as string), 
            Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort as string,
            (sortOrder as string) === 'asc' ? 'asc' : 'desc'
        );
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Promptings retrieved successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Lấy promptings theo status (active/inactive)
export const getPromptingsByStatus = async (req: Request, res: Response) => {
    try {
        const { status } = req.params; // 'active' hoặc 'inactive'
        const { 
            page = 1, 
            limit = 10, 
            sort = '_id', 
            sortOrder = 'desc' 
        } = req.query;
        
        // Convert status string to number
        let active_yn: number;
        if (status === 'active') {
            active_yn = 1;
        } else if (status === 'inactive') {
            active_yn = 0;
        } else {
            return res.status(400).json({
                success: false,
                message: 'Status must be "active" or "inactive"'
            });
        }
        
        const result = await promptingsService.getPromptingsByStatus(
            active_yn, 
            parseInt(page as string), 
            Math.min(100, parseInt(limit as string)), // Giới hạn tối đa 100 items
            sort as string,
            (sortOrder as string) === 'asc' ? 'asc' : 'desc'
        );
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Promptings retrieved successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Bulk delete promptings
export const bulkDeletePromptings = async (req: Request, res: Response) => {
    try {
        const { ids } = req.body;
        
        if (!Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'IDs array is required and must not be empty'
            });
        }
        
        // Convert string IDs to ObjectIds for filter
        const objectIds = ids.map(id => ({ _id: { $in: ids.map(id => id) } }));
        const filter = { _id: { $in: ids } };
        
        const result = await promptingsService.deleteManyPromptings(filter);
        
        res.status(200).json({
            success: true,
            message: 'Promptings deleted successfully',
            data: result.data
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

// Bulk update promptings
export const bulkUpdatePromptings = async (req: Request, res: Response) => {
    try {
        const { ids, updateData } = req.body;
        
        if (!Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'IDs array is required and must not be empty'
            });
        }
        
        if (!updateData || typeof updateData !== 'object') {
            return res.status(400).json({
                success: false,
                message: 'Update data is required and must be an object'
            });
        }
        
        const filter = { _id: { $in: ids } };
        
        const result = await promptingsService.updateManyPromptings(filter, updateData);
        
        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Promptings updated successfully',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};
