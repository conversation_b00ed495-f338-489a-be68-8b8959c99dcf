/** */
import express from 'express';
const morgan = require('morgan');
import cors from 'cors';

import application from '../constants/application';
import indexRoute from '../routes/index.route';
import joiErrorHandler from '../middlewares/joiErrorHandler';
import * as errorHandler from '../middlewares/apiErrorHandler';
import logger from '../config/logger';

const app = express();

if (process.env.CORS === 'true') {
    app.use(cors());
}

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));
app.use(morgan('dev'));

// Middleware log request

app.use((req:any, res:any, next) => {
  logger.info("\n")
  logger.info("|----------------------------------------------------------------------------------|");
  logger.info("| ------------------------------- Incoming Request ------------------------------- |");
  logger.info(`| ==> ${req.method} ${req.url}`);
  logger.info("| ==> Headers: " +  JSON.stringify(req.headers));
  logger.info("| ==> Query Params: " + JSON.stringify( req.query));
  logger.info("| ==> Params: " + JSON.stringify( req.params));
  logger.info("| ==> Body: "+ JSON.stringify(req.body));
  const oldSend = res.send;
  res.send = function (data:any) {
    if(!JSON.parse(data)?.error){
      logger.info("| ----------------------------- Response ----------------------------------------- |");
      logger.info("| ==> Response data: " + JSON.stringify(data));
    }
    logger.info("|----------------------------------------------------------------------------------|");
    oldSend.apply(res, arguments); // Gọi hàm gốc
  };
  next(); // Chuyển tiếp request tới route tiếp theo
});
// Router
app.use(application.url.base, indexRoute);

// Joi Error Handler
app.use(joiErrorHandler);
// Error Handler
app.use(errorHandler.notFoundErrorHandler);

app.use(errorHandler.errorHandler);

export default app;
