import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';
import getGoogleToken from '../services/googleTokenServices'
const hashSHA256 = process.env.DIR_HEADER_SHA256_ORDER || "0a0c6a03c9882f0d54b325f5b4ee170d8ef8fa1d0f598d6ba791750ac2b441fa" //shinhan@2022
const googleToken : IController = async (req, res) =>{   
    // thực thi gọi API  
    try {
        const { body, headers }= req;
        logger.info("-----------------Start GoogleToken-----------------------");
        logger.info('body, headers: ', body, headers);
        if( !headers['ssv-header-google'] || headers['ssv-header-google'] && headers['ssv-header-google'] != hashSHA256){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
            ); 
            return   
        } 
        
        const result:any = await getGoogleToken.callGoogleToken(body)
        logger.info(JSON.stringify(result));
        apiResponse.result(res, { 
            responseServer: 'ok',
            response: {
                is_type: body.is_type,
                verify_code: body.verify_code, 
                data: JSON.stringify(result), 
                status: result.success ? '1':'0',
                errorCode: JSON.stringify(result['error-codes'])
        } });
        logger.info("-----------------End GoogleToken-----------------------");

    } catch (e) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
} 

export default {
    googleToken
}   
