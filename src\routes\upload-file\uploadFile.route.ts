import express from 'express';
import uploadSchema from '../../constants/schema/upload.schema';
import { celebrate } from 'celebrate';
import uploadController from '../../controllers/upload.controller';

const router = express.Router();

router.post(
  '/',
  celebrate(uploadSchema.upload),
  uploadController.uploadFeedbackImages,
);

router.get(
  '/*',
  uploadController.getImageFeedback,
);


export default router;

