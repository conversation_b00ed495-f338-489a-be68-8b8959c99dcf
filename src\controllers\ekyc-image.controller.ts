import httpStatusCodes from 'http-status-codes';
import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import locale from '../constants/locale';
import logger from '../config/logger';
import insEkycImageService from '../services/ekycImageService';

const getImageEkyc : IController = async (req, res) => {
    try {
        const { cusNo = '' } =  req.query;
        const result = insEkycImageService.getImage(cusNo.toString())
        apiResponse.result(res, {
            result: result.data,
        },                 result.httpStatusCodes);
        return
    } catch (error) {
        console.log('error: ', error);
        logger.error('getImageEkyc', {
            error,
        });
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
}

export default {
    getImageEkyc,
};
