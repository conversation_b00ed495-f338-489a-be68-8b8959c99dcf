// // Example file showing how to integrate chat-topic routes into your Express app
// // This is just an example - adapt to your existing app structure

// import express from 'express';
// import cors from 'cors';
// import chatTopicRoutes from './routes/history-chat/chat-topic.routes';

// const app = express();

// // Middleware
// app.use(cors());
// app.use(express.json());
// app.use(express.urlencoded({ extended: true }));

// // Error handling middleware
// app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
//     console.error('Error:', err);
//     res.status(500).json({
//         success: false,
//         message: 'Internal server error',
//         error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
//     });
// });

// // Routes
// app.use('/api', chatTopicRoutes);

// // Health check endpoint
// app.get('/health', (req, res) => {
//     res.json({
//         success: true,
//         message: 'Server is running',
//         timestamp: new Date().toISOString()
//     });
// });

// // 404 handler
// app.use('*', (req, res) => {
//     res.status(404).json({
//         success: false,
//         message: 'Route not found',
//         path: req.originalUrl
//     });
// });

// const PORT = process.env.PORT || 3000;

// app.listen(PORT, () => {
//     console.log(`Server is running on port ${PORT}`);
//     console.log(`Health check: http://localhost:${PORT}/health`);
//     console.log(`API Documentation: See src/docs/chat-api-documentation.md`);
// });

// export default app;
