# Chat Topic API Documentation

## Overview
API này cung cấp các chức năng CRUD (Create, Read, Update, Delete) cho ChatCusTopic và ChatCusTopicMsg.

## Base URL
```
/api
```

## Response Format
Tất cả API responses đều có format sau:
```json
{
  "success": boolean,
  "message": string,
  "data": object | array,
  "errors": string[] (optional)
}
```

---

## ChatCusTopic APIs

### 1. Tạo Chat Topic Mới
**POST** `/chat-topics`

**Request Body:**
```json
{
  "fos_id": "string (required)",
  "topic_id": "string (required)", 
  "topic_name": "string (required)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chat topic created successfully",
  "data": {
    "insertedId": "string"
  }
}
```

### 2. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>t <PERSON>
**PUT** `/chat-topics/:fos_id/:topic_id`

**Parameters:**
- `fos_id`: FOS ID (string)
- `topic_id`: Topic ID (string)

**Request Body:**
```json
{
  "topic_name": "string (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Chat topic updated successfully",
  "data": {
    "modifiedCount": 1
  }
}
```

### 3. Xóa Chat Topic
**DELETE** `/chat-topics/:fos_id/:topic_id`

**Parameters:**
- `fos_id`: FOS ID (string)
- `topic_id`: Topic ID (string)

**Response:**
```json
{
  "success": true,
  "message": "Chat topic deleted successfully",
  "data": {
    "deletedCount": 1
  }
}
```

### 4. Lấy Danh Sách Chat Topics
**GET** `/chat-topics`

**Query Parameters:**
- `page`: Số trang (number, default: 1)
- `limit`: Số items per page (number, default: 10, max: 100)
- `sort`: Trường để sắp xếp (string, default: 'created_at')
- `sortOrder`: Thứ tự sắp xếp (string, 'asc' | 'desc', default: 'desc')
- `fos_id`: Filter theo FOS ID (string, optional)
- `topic_name`: Filter theo topic name (string, optional)

**Response:**
```json
{
  "success": true,
  "message": "Chat topics retrieved successfully",
  "data": {
    "data": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 100,
      "pages": 10,
      "sort": "created_at",
      "sortOrder": "desc",
      "hasNextPage": true,
      "hasPrevPage": false,
      "nextPage": 2,
      "prevPage": null,
      "currentPage": 1,
      "totalPages": 10,
      "totalItems": 100,
      "itemsPerPage": 10
    }
  }
}
```

### 5. Lấy Chat Topic Theo ID
**GET** `/chat-topics/:fos_id/:topic_id`

**Parameters:**
- `fos_id`: FOS ID (string)
- `topic_id`: Topic ID (string)

**Response:**
```json
{
  "success": true,
  "message": "Chat topic retrieved successfully",
  "data": {
    "_id": "string",
    "fos_id": "string",
    "topic_id": "string",
    "topic_name": "string",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### 6. Lấy Chat Topics Theo FOS ID
**GET** `/chat-topics/fos/:fos_id`

**Parameters:**
- `fos_id`: FOS ID (string)

**Query Parameters:**
- `page`: Số trang (number, default: 1)
- `limit`: Số items per page (number, default: 10, max: 100)
- `sort`: Trường để sắp xếp (string, default: 'created_at')
- `sortOrder`: Thứ tự sắp xếp (string, 'asc' | 'desc', default: 'desc')

**Response:**
```json
{
  "success": true,
  "message": "Chat topics retrieved successfully",
  "data": {
    "data": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5,
      "sort": "created_at",
      "sortOrder": "desc",
      "hasNextPage": true,
      "hasPrevPage": false,
      "nextPage": 2,
      "prevPage": null,
      "currentPage": 1,
      "totalPages": 5,
      "totalItems": 50,
      "itemsPerPage": 10
    }
  }
}
```

---

## ChatCusTopicMsg APIs

### 1. Tạo Message Mới
**POST** `/messages`

**Request Body:**
```json
{
  "topic_id": "string (required)",
  "message": "string (required)",
  "sender": "string (required)",
  "reciever": "string (required)",
  "reaction": "number (optional, default: 0)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message created successfully",
  "data": {
    "insertedId": "string"
  }
}
```

### 2. Cập Nhật Message
**PUT** `/messages/:messageId`

**Parameters:**
- `messageId`: Message ID (string)

**Request Body:**
```json
{
  "message": "string (optional)",
  "reaction": "number (optional)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message updated successfully",
  "data": {
    "modifiedCount": 1
  }
}
```

### 3. Xóa Message
**DELETE** `/messages/:messageId`

**Parameters:**
- `messageId`: Message ID (string)

**Response:**
```json
{
  "success": true,
  "message": "Message deleted successfully",
  "data": {
    "deletedCount": 1
  }
}
```

### 4. Lấy Messages Theo Topic ID
**GET** `/messages/topic/:topicId`

**Parameters:**
- `topicId`: Topic ID (string)

**Query Parameters:**
- `page`: Số trang (number, default: 1)
- `limit`: Số items per page (number, default: 10, max: 100)
- `sort`: Trường để sắp xếp (string, default: 'created_at')
- `sortOrder`: Thứ tự sắp xếp (string, 'asc' | 'desc', default: 'desc')

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "data": [...],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3,
      "sort": "created_at",
      "sortOrder": "desc",
      "hasNextPage": true,
      "hasPrevPage": false,
      "nextPage": 2,
      "prevPage": null,
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10
    }
  }
}
```

### 5. Cập Nhật Reaction
**PATCH** `/messages/:messageId/reaction`

**Parameters:**
- `messageId`: Message ID (string)

**Request Body:**
```json
{
  "reaction": "number (required)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message reaction updated successfully",
  "data": {
    "modifiedCount": 1
  }
}
```

---

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    "fos_id is required and must be a valid string",
    "topic_name is required and must be a valid string"
  ]
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "Resource not found"
}
```

### Internal Server Error (500)
```json
{
  "success": false,
  "message": "Internal server error",
  "error": "Error details..."
}
```

---

## Data Models

### ChatCusTopic
```typescript
interface ChatCusTopic {
  _id?: string;
  fos_id: string;
  topic_id: string;
  topic_name: string;
  created_at: Date;
  updated_at: Date;
}
```

### ChatCusTopicMsg
```typescript
interface ChatCusTopicMsg {
  _id?: string;
  topic_id: string;
  message: string;
  sender: string;
  reaction: number;
  reciever: string;
  created_at: Date;
  updated_at: Date;
}
```
