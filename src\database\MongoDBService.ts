import { MongoDBClient } from '../database/mongodb';
import { Collection, InsertOneResult, InsertManyResult, UpdateResult, DeleteResult, FindO<PERSON>s, Filter, UpdateFilter, OptionalUnlessRequiredId, WithId, Document } from 'mongodb';
import logger from '../config/logger';

/**
 * MongoDB Service - Service chung cho các operations CRUD
 * Sử dụng MongoDBClient hiện có để thực hiện các thao tác database
 */
export class MongoDBService {
    private static instance: MongoDBService;

    private constructor() {}

    public static getInstance(): MongoDBService {
        if (!MongoDBService.instance) {
            MongoDBService.instance = new MongoDBService();
        }
        return MongoDBService.instance;
    }

    /**
     * INSERT Operations
     */

    // Insert một document
    async insertOne<T extends Document>(
        collectionName: string, 
        document: OptionalUnlessRequiredId<T>
    ): Promise<InsertOneResult<T>> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.insertOne(document);
            
            logger.info(`Document inserted into ${collectionName}:`, result.insertedId);
            return result;
        } catch (error) {
            logger.error(`Failed to insert document into ${collectionName}:`, error);
            throw error;
        }
    }

    // Insert nhiều documents
    async insertMany<T extends Document>(
        collectionName: string, 
        documents: OptionalUnlessRequiredId<T>[]
    ): Promise<InsertManyResult<T>> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.insertMany(documents);
            
            logger.info(`${documents.length} documents inserted into ${collectionName}`);
            return result;
        } catch (error) {
            logger.error(`Failed to insert documents into ${collectionName}:`, error);
            throw error;
        }
    }

    /**
     * SELECT Operations
     */

    // Tìm một document
    async findOne<T extends Document>(
        collectionName: string, 
        filter: Filter<T>, 
        options?: FindOptions<T>
    ): Promise<WithId<T> | null> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.findOne(filter, options);
            
            logger.debug(`FindOne query executed on ${collectionName}`);
            return result;
        } catch (error) {
            logger.error(`Failed to find document in ${collectionName}:`, error);
            throw error;
        }
    }

    // Tìm nhiều documents
    async findMany<T extends Document>(
        collectionName: string, 
        filter: Filter<T> = {}, 
        options?: FindOptions<T>
    ): Promise<WithId<T>[]> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const cursor = collection.find(filter, options);
            const result = await cursor.toArray();
            
            logger.debug(`FindMany query executed on ${collectionName}, found ${result.length} documents`);
            return result;
        } catch (error) {
            logger.error(`Failed to find documents in ${collectionName}:`, error);
            throw error;
        }
    }

    // Tìm với pagination
    async findWithPagination<T extends Document>(
        collectionName: string,
        filter: Filter<T> = {},
        page: number = 1,
        limit: number = 10,
        sort?: any
    ): Promise<{
        data: WithId<T>[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            pages: number;
        };
    }> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const skip = (page - 1) * limit;
            
            // Đếm tổng số documents
            const total = await collection.countDocuments(filter);
            
            // Lấy data với pagination
            let cursor = collection.find(filter).skip(skip).limit(limit);
            if (sort) {
                cursor = cursor.sort(sort);
            }
            
            const data = await cursor.toArray();
            
            const pagination = {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            };
            
            logger.debug(`Paginated query executed on ${collectionName}: page ${page}, limit ${limit}, total ${total}`);
            
            return { data, pagination };
        } catch (error) {
            logger.error(`Failed to execute paginated query on ${collectionName}:`, error);
            throw error;
        }
    }

    // Đếm documents
    async count<T extends Document>(
        collectionName: string, 
        filter: Filter<T> = {}
    ): Promise<number> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const count = await collection.countDocuments(filter);
            
            logger.debug(`Count query executed on ${collectionName}: ${count} documents`);
            return count;
        } catch (error) {
            logger.error(`Failed to count documents in ${collectionName}:`, error);
            throw error;
        }
    }

    /**
     * UPDATE Operations
     */

    // Update một document
    async updateOne<T extends Document>(
        collectionName: string, 
        filter: Filter<T>, 
        update: UpdateFilter<T>
    ): Promise<UpdateResult> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.updateOne(filter, update);
            
            logger.info(`UpdateOne executed on ${collectionName}: ${result.modifiedCount} document(s) modified`);
            return result;
        } catch (error) {
            logger.error(`Failed to update document in ${collectionName}:`, error);
            throw error;
        }
    }

    // Update nhiều documents
    async updateMany<T extends Document>(
        collectionName: string, 
        filter: Filter<T>, 
        update: UpdateFilter<T>
    ): Promise<UpdateResult> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.updateMany(filter, update);

            logger.info(`UpdateMany executed on ${collectionName}: ${result.modifiedCount} document(s) modified`);
            return result as UpdateResult;
        } catch (error) {
            logger.error(`Failed to update documents in ${collectionName}:`, error);
            throw error;
        }
    }

    // Update hoặc Insert (upsert)
    async upsert<T extends Document>(
        collectionName: string, 
        filter: Filter<T>, 
        update: UpdateFilter<T>
    ): Promise<UpdateResult> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.updateOne(filter, update, { upsert: true });

            logger.info(`Upsert executed on ${collectionName}: ${result.upsertedCount ? 'inserted' : 'updated'}`);
            return result as UpdateResult;
        } catch (error) {
            logger.error(`Failed to upsert document in ${collectionName}:`, error);
            throw error;
        }
    }

    /**
     * DELETE Operations
     */

    // Delete một document
    async deleteOne<T extends Document>(
        collectionName: string, 
        filter: Filter<T>
    ): Promise<DeleteResult> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.deleteOne(filter);
            
            logger.info(`DeleteOne executed on ${collectionName}: ${result.deletedCount} document(s) deleted`);
            return result;
        } catch (error) {
            logger.error(`Failed to delete document in ${collectionName}:`, error);
            throw error;
        }
    }

    // Delete nhiều documents
    async deleteMany<T extends Document>(
        collectionName: string, 
        filter: Filter<T>
    ): Promise<DeleteResult> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.deleteMany(filter);
            
            logger.info(`DeleteMany executed on ${collectionName}: ${result.deletedCount} document(s) deleted`);
            return result;
        } catch (error) {
            logger.error(`Failed to delete documents in ${collectionName}:`, error);
            throw error;
        }
    }

    /**
     * UTILITY Operations
     */

    // Kiểm tra collection có tồn tại không
    async collectionExists(collectionName: string): Promise<boolean> {
        try {
            const db = MongoDBClient.getDb();
            const collections = await db.listCollections({ name: collectionName }).toArray();
            return collections.length > 0;
        } catch (error) {
            logger.error(`Failed to check collection existence: ${collectionName}:`, error);
            throw error;
        }
    }

    // Tạo index
    async createIndex<T extends Document>(
        collectionName: string, 
        indexSpec: any, 
        options?: any
    ): Promise<string> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.createIndex(indexSpec, options);
            
            logger.info(`Index created on ${collectionName}:`, result);
            return result;
        } catch (error) {
            logger.error(`Failed to create index on ${collectionName}:`, error);
            throw error;
        }
    }

    // Drop collection
    async dropCollection(collectionName: string): Promise<boolean> {
        try {
            const db = MongoDBClient.getDb();
            const result = await db.dropCollection(collectionName);
            
            logger.info(`Collection dropped: ${collectionName}`);
            return result;
        } catch (error) {
            logger.error(`Failed to drop collection: ${collectionName}:`, error);
            throw error;
        }
    }

    // Aggregate
    async aggregate<T extends Document>(
        collectionName: string, 
        pipeline: any[]
    ): Promise<any[]> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const cursor = collection.aggregate(pipeline);
            const result = await cursor.toArray();
            
            logger.debug(`Aggregate query executed on ${collectionName}, returned ${result.length} results`);
            return result;
        } catch (error) {
            logger.error(`Failed to execute aggregate on ${collectionName}:`, error);
            throw error;
        }
    }

    // Distinct
    async distinct<T extends Document>(
        collectionName: string, 
        field: string, 
        filter: Filter<T> = {}
    ): Promise<any[]> {
        try {
            const collection = MongoDBClient.getCollection<T>(collectionName);
            const result = await collection.distinct(field, filter);
            
            logger.debug(`Distinct query executed on ${collectionName}.${field}, returned ${result.length} values`);
            return result;
        } catch (error) {
            logger.error(`Failed to execute distinct on ${collectionName}.${field}:`, error);
            throw error;
        }
    }
}

// Export singleton instance
const mongoDBService = MongoDBService.getInstance();
export default mongoDBService;
