import IController from 'IController';
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';
import process from 'process';
import uploadImageCallService from '../services/UploadImageCallService';
import logger from '../config/logger';

const hashSHA256 = process.env.UPLOAD_VIDEO_CALL_HEADER || 'e6e5176bcaadf7ee65988fa110a1cc6c4589fc4ee8beb11a22b2cc3f42d3bfb3' // thisisthekeyforstringee
const uploadImage: IController = async (req, res) => {
  console.log('==========START UPLOAD IMAGE STRINGEE==========', req, res)
  try {
    const { headers } = req;
    if (!headers['x-image-video-call-x']
            || (headers['x-image-video-call-x']
                && headers['x-image-video-call-x'] !== hashSHA256)
        ) {
      console.log('Thieu header authenticate roi')
      apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
      return
    }

    const result = await uploadImageCallService.uploadImage(req)
    apiResponse.result(res, result);
    console.log('==========END==========')
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            err.message,
        );
  }
}

const downloadImage : IController = async (req, res) => {
  try {
    const { headers, body } = req;
    if (!headers['x-image-video-call-x']
            || (headers['x-image-video-call-x']
                && headers['x-image-video-call-x'] !== hashSHA256)
        ) {
      console.log('Headers Invalid')
      apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
      return
    }
    const { filename } = body
    if(!filename || filename && filename.length == 0){
      apiResponse.error(
        res,
        httpStatusCodes.BAD_REQUEST,
        'filename invalid',
      );
      return
    }
    const result = await uploadImageCallService.downloadImage(filename)
    apiResponse.result(res, result);
    return
  } catch (error) {
    logger.error( `REQUEST_ERROR: downloadImage`, error );
    apiResponse.error(
      res,
      httpStatusCodes.BAD_REQUEST,
      error.message,
    );
  }
}

const uploadVideo: IController = async (req, res) => {
  console.log('==========START UPLOAD VIDEO STRINGEE==========')
  try {
    const { headers } = req;
    if (!headers['x-image-video-call-x']
            || (headers['x-image-video-call-x']
                && headers['x-image-video-call-x'] !== hashSHA256)
        ) {
      console.log('Thieu header authenticate roi')
      apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
      return
    }

    const result = await uploadImageCallService.uploadVideo(req)
    apiResponse.result(res, result);
    console.log('==========END==========')
    return
  } catch (err) {
    console.log('error: ', err);
    apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            err.message,
        );
  }
}

export default {
  uploadImage,
  downloadImage,
  uploadVideo
}
