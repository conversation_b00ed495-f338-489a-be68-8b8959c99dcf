import httpStatusCodes from 'http-status-codes';

import { Response } from 'express';
import fs from 'fs';
import moment from 'moment';
import multiparty from 'multiparty';
import path from 'path';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import locale from '../constants/locale';
import glb_sv from '../share/global_service';
import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';

const hashSHA256 =
    process.env.AVATAR_UPLOAD_HEADER ||
    '85680a1aeb82a95c561838e9bb93102872fdbdc59a9b4e69d162c0dd1e241fb9';

const upload: IController = async (req, res) => {
    const form = new multiparty.Form();
    form.parse(req, async function (err, fields, files) {
        if (err) {
            console.error(err);
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
            return;
        }
        console.log('upload file client request', fields, files);

        if (!fields.fileName || !fields.fileName.length) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log('REQUEST_ERROR fileNameFace');
            return;
        }
        let fileName = fields.fileName[0];
        if (!files.file || !files.file.length) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log('REQUEST_ERROR file');
            return;
        }
        const file = files.file[0];
        if (!file || !file.path) {
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            console.log('REQUEST_ERROR file');
            return;
        }

        const fileType = fields.fileType ? fields.fileType[0] : '';
        try {
            const nameOrigin =
                uuidv4() + '.' + glb_sv.getTypeFile(fileName);
            const nameThumbnail = uuidv4() + '.jpeg';

            const pathFileOrgin = path.join(
                process.cwd(),
                process.env.DB_LOCAL,
                '/uploads/',
                nameOrigin,
            );
            const pathFileThumbnail = path.join(
                process.cwd(),
                process.env.DB_LOCAL,
                '/uploads/',
                nameThumbnail,
            );

            const reduceFace = !glb_sv.isImage(fileName)
                ? {
                      buffer: fs.readFileSync(file.path),
                      base64: '',
                  }
                : await glb_sv.resizeImage(file.path, 300);

            if (reduceFace.buffer === '') {
                apiResponse.error(
                    res,
                    httpStatusCodes.BAD_REQUEST,
                    locale.API_ALT_ERROR,
                );
                return;
            }

            if (fileType.includes('image')) {
                const promises = [];
                promises.push(
                    glb_sv.writeToFile(
                        pathFileOrgin,
                        fs.readFileSync(file.path),
                    ),
                );
                promises.push(
                    glb_sv.writeToFile(
                        pathFileThumbnail,
                        reduceFace.buffer,
                    ),
                );
                Promise.all(promises)
                    .then((resPromises) => {
                        if (resPromises[0] && resPromises[1]) {
                            const statFile = fs.statSync(pathFileOrgin);

                            apiResponse.result(res, {
                                name: fileName,
                                type: fileType || '',
                                urlOrigin:
                                    'https://' +
                                    req.get('host') +
                                    '/uploads/' +
                                    nameOrigin,
                                urlThumbnail:
                                    'https://' +
                                    req.get('host') +
                                    '/uploads/' +
                                    nameThumbnail,
                                size: statFile.size,
                            });
                        } else {
                            apiResponse.error(
                                res,
                                httpStatusCodes.BAD_REQUEST,
                                locale.API_ALT_ERROR,
                            );
                        }
                    })
                    .catch((err) => {
                        console.log(
                            '🚀 ~ file: upload.controller.ts ~ line 122 ~ Promise.all ~ err',
                            err,
                        );
                        apiResponse.error(
                            res,
                            httpStatusCodes.BAD_REQUEST,
                            locale.API_ALT_ERROR,
                        );
                    });
            } else {
                const isWriteFile = await glb_sv.writeToFile(
                    pathFileOrgin,
                    reduceFace.buffer,
                );
                if (isWriteFile) {
                    const statFile = fs.statSync(pathFileOrgin);
                    apiResponse.result(res, {
                        name: fileName,
                        type: fileType || '',
                        urlOrigin:
                            'https://' +
                            req.get('host') +
                            '/uploads/' +
                            nameOrigin,
                        size: statFile.size,
                    });
                } else {
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        locale.API_ALT_ERROR,
                    );
                }
            }
        } catch (error) {
            console.error(error);
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.API_ALT_ERROR,
            );
        }
    });
};

const deleteFile: IController = async (req, res) => {
    console.log('deleteFile client request', req.body);
    let listFile = req.body.files;
    if (!listFile || listFile.length === 0) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
        return;
    }
    const promises: any[] = [];
    listFile.forEach((element: string) => {
        if (typeof element !== 'string') {
        } else {
            const nameFile =
                element.split('/')[element.split('/').length - 1];
            if (nameFile) {
                const pathFileThumbnail = path.join(
                    process.cwd(),
                    process.env.DB_LOCAL,
                    '/uploads/',
                    nameFile,
                );
                promises.push(fs.promises.unlink(pathFileThumbnail));
            }
        }
    });
    if (!promises.length) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
        return;
    }
    Promise.all(promises)
        .then((resPromises) => {
            console.log(
                '🚀 ~ file: upload.controller.ts ~ line 180 ~ Promise.all ~ resPromises',
                resPromises,
            );
            apiResponse.result(res, {});
        })
        .catch((err) => {
            console.log(
                '🚀 ~ file: upload.controller.ts ~ line 194 ~ Promise.all ~ err',
                err,
            );
            apiResponse.error(
                res,
                httpStatusCodes.BAD_REQUEST,
                locale.REQUEST_ERROR,
            );
            return;
        });
};

const uploadFeedbackImages: IController = async (req, res) => {
    let feedbackFolder = '';
    try {
        const { headers } = req;
        if (
            !headers['x-upload-header'] ||
            (headers['x-upload-header'] &&
                headers['x-upload-header'] !== hashSHA256)
        ) {
            apiResponse.error(res, httpStatusCodes.FORBIDDEN);
            return;
        }

        const rootFolderImage =
            process.env.DIR_UPLOAD_IMAGE_FEEDBACK ||
            'temp-upload/FEEDBACK_IMAGE';
        const rootPathImage =
            process.env.PATH_IMAGE_FEEDBACK ||
            'api/upload/FEEDBACK_IMAGE';

        const form = new multiparty.Form({
            maxFilesSize: 5242880, /// 5Mb to bytes
        });

        form.parse(req, async function (err, fields, files) {
            try {
                if (err) {
                    return apiResponse.error(
                        res,
                        httpStatusCodes.INTERNAL_SERVER_ERROR,
                        err.message,
                    );
                }

                console.log('🚀 ~ uploadFeedbackImages ~ start ~');

                if (!fields.fos_no) {
                    console.log(
                        '🚀 ~ uploadFeedbackImages ~ not found [fos_no] from form ~',
                    );
                    return apiResponse.error(
                        res,
                        httpStatusCodes.INTERNAL_SERVER_ERROR,
                        'Not found fos_no',
                    );
                }

                /// Kiểm tra số lượng file nếu rỗng
                /// hoặc nhiều hơn 3 thì báo lỗi
                if (
                    !files.files ||
                    !files.files.length ||
                    files.files.length > 3 ||
                    err
                ) {
                    console.log(
                        '🚀 ~ uploadFeedbackImages ~ [files] not found or over 3 files ~',
                    );
                    apiResponse.error(
                        res,
                        httpStatusCodes.BAD_REQUEST,
                        'Image maximum is 3 and minimum is 1',
                    );
                    return;
                }

                const userFbFolderName = fields.fos_no[0];

                const userFbFoldePath = path.join(
                    process.cwd(),
                    `${rootFolderImage}`,
                    userFbFolderName,
                );

                /// Kiểm tra [userFbFoldePath] tồn tại
                /// Không tồn tại thì tạo mới
                /// Nếu tồn tại thì bỏ qua
                if (!fs.existsSync(userFbFoldePath)) {
                    fs.mkdirSync(userFbFoldePath, { recursive: true });
                }

                /// Kiểm tra [feedbackFolder] tồn tại
                /// Không tồn tại thì tạo mới
                /// Nếu tồn tại thì bỏ qua
                const feedbackFolderName = uuidv4();
                feedbackFolder = path.join(
                    process.cwd(),
                    `${rootFolderImage}`,
                    userFbFolderName,
                    feedbackFolderName,
                );
                if (!fs.existsSync(feedbackFolder)) {
                    fs.mkdirSync(feedbackFolder, { recursive: true });
                }

                ///
                let imageUrls = [];
                for (let i = 0; i < files.files.length; i++) {
                    console.log(
                        '🚀 ~ uploadFeedbackImages ~ start save file %s ~',
                        i + 1,
                    );
                    const file = files.files[i];
                    /// Force convert format fo jpeg
                    const nameOrigin = uuidv4() + '.jpeg';
                    const imagePath = path.join(
                        feedbackFolder,
                        nameOrigin,
                    );
                    /// Format and save file
                    await sharp(file.path)
                        .withMetadata()
                        .jpeg()
                        .toFile(imagePath)
                        .catch((e) => {
                            console.log(
                                '🚀 ~ uploadFeedbackImages ~ error ~',
                            );
                            return handleErrorFeedbackImage(
                                feedbackFolder,
                                res,
                            );
                        });

                    /// Add image URL to response
                    const returnPath = path.join(
                        rootPathImage,
                        userFbFolderName,
                        feedbackFolderName,
                        nameOrigin,
                    );

                    imageUrls.push(
                        `${req.protocol}://${req.get('host')}/` +
                            returnPath,
                    );

                    console.log(
                        '🚀 ~ uploadFeedbackImages ~ end save file %s ~',
                        i + 1,
                    );
                }
                console.log('🚀 ~ uploadFeedbackImages ~ end ~');
                return apiResponse.result(res, {
                    data: imageUrls,
                });
            } catch (error) {
                console.log('🚀 ~ uploadFeedbackImages ~ error ~');
                return handleErrorFeedbackImage(feedbackFolder, res);
            }
        });
    } catch (e) {
        console.log('🚀 ~ uploadFeedbackImages ~ error ~');
        return handleErrorFeedbackImage(feedbackFolder, res);
    }
};

function handleErrorFeedbackImage(folderPath: string, res: Response) {
    /// Xoá folder nếu xuất hiện lỗi
    if (!fs.existsSync(folderPath)) {
        fs.rmSync(folderPath, { recursive: true });
    }

    return apiResponse.error(
        res,
        httpStatusCodes.INTERNAL_SERVER_ERROR,
        locale.API_ALT_ERROR,
    );
}

const getImageFeedback: IController = async (req, res) => {
    const imagePathRequest = req.params[0];

    const imagePathResult = path.join(process.env.DIR_UPLOAD_IMG, imagePathRequest);

    /// Kiểm tra nếu không thấy thì response error
    if (!fs.existsSync(imagePathResult)) {
        return apiResponse.error(
            res,
            httpStatusCodes.INTERNAL_SERVER_ERROR,
            'Not found image',
        );
    }

    return res.sendFile(imagePathResult, { root: '.' });
};

export default {
    upload,
    deleteFile,
    uploadFeedbackImages,
    getImageFeedback,
};
