import { Request, Response } from 'express';
import FAService from "../services/FAService"
import apiResponse from '../utilities/apiResponse';
import httpStatusCodes from 'http-status-codes';

// ============ Fireant Controllers ============
const hashSHA256 = process.env.FIREANT_HEADER || 'e6e5176bcaadf7ee65988fa110a1cc6c4589fc4ee8beb11a22b2cc3f42d3bfb3' // keyforcheckbadword

// Lấy accessToken Fireant
export const GetAccessToken = async (req: Request, res: Response) => {
    const { headers } = req;
    if (!headers['x-fireant-x']
        || (headers['x-fireant-x']
            && headers['x-fireant-x'] !== hashSHA256)
    ) {
        apiResponse.error(
            res,
            httpStatusCodes.FORBIDDEN,
        );
        return
    }

    try {
        const result = await FAService.getTokenFA();

        if (result.success) {
            res.status(200).json({
                success: true,
                message: 'Thực hiện thành công!',
                data: result.data
            });
        } else {
            res.status(400).json({
                success: false,
                message: result.message
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
};

export default {
  GetAccessToken,
}
