import IController from '../types/IController';
import apiResponse from '../utilities/apiResponse';
import reportService from '../services/ReportService';
import httpStatusCodes from 'http-status-codes';
import locale from '../constants/locale';
import logger from '../config/logger';


const hashSHA256Report = process.env.DIR_HEADER_SHA256_REPORT || "4d311062c07e756f59cf212075935ec635f3db8f066cd1f3f8cf2e35fdc47dfd" //Shinhan@2023
const readFileStream: IController = async (req, res) => {
    const { body, headers }= req;
    const {filename } = body
    if( !headers['tssvheaderrepor'] || headers['tssvheaderrepor'] && headers['tssvheaderrepor'] != hashSHA256Report){
        apiResponse.error(
            res,
            httpStatusCodes.FORBIDDEN,
        );
        return 
    }
    if(!filename){ 
        return  apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.REQUEST_ERROR,
        );
    }
    try {
        const getContentFromFile = await reportService.readFileStream(filename);
        apiResponse.result(res, { response: {
            body: getContentFromFile.data,
            clientCode: getContentFromFile.clientCode
        } });
    } catch (e) {
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
}
const json2file: IController= async (req, res) => {
     // thực thi gọi API  
     try {
        const { body, headers }= req;
        logger.info(JSON.stringify(body));
        logger.info("-----------------Start PostBack json2file-----------------------");
        logger.info('body, headers: ', body, headers);
        if( !headers['tssvheaderrepor'] || headers['tssvheaderrepor'] && headers['tssvheaderrepor'] != hashSHA256Report){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
            ); 
            return   
        } 
        const {data , filename} = body
        if(!data || !filename){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN, 
                "Dữ liệu đầu vào không đúng. Vui lòng thử lại!"
            ); 
            return   
        }
        const result:any = await reportService.json2file({data , filename})
        logger.info(JSON.stringify(result));
        apiResponse.result(res, { 
            responseServer: 'ok',
            response: {
                body: body,
                clientCode: result
            }            
         });
        logger.info("-----------------End PostBack json2file-----------------------");
    } catch (e) {
        logger.info("----------------Error End PostBack json2file-----------------------");
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }

}

const fosReport: IController = async (req, res) => {
    // thực thi gọi API
    try {
        const { body, headers } = req;
        logger.info(JSON.stringify(body));
        logger.info(
            '-----------------Start call report API-----------------------',
        );
        logger.info('body, headers: ', body, headers);
        if( !headers['tssvheaderrepor'] || headers['tssvheaderrepor'] && headers['tssvheaderrepor'] != hashSHA256Report){
            apiResponse.error(
                res,
                httpStatusCodes.FORBIDDEN,
            );
            return 
        }

        const result: any = await reportService.getFOSReport(body);
        logger.info(JSON.stringify(result));
        apiResponse.result(res, result);
        logger.info(
            '-----------------End call report API-----------------------',
        );
    } catch (e) {
        logger.info(
            '----------------Error call report API-----------------------',
        );
        apiResponse.error(
            res,
            httpStatusCodes.BAD_REQUEST,
            locale.API_ALT_ERROR,
        );
    }
};



export default {
    readFileStream,
    json2file,
    fosReport
}