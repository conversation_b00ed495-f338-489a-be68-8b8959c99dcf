import * as process from 'process';
import axios from 'axios';
import logger from '../config/logger';
import _ from 'lodash';
import IRequest from 'IRequest';

const badWordUrl = process.env.BADWORD_URL;
interface RequestBodyCheckBadWord {
  body: {
    sentence: string,
  }
}

class BadWordFilterService {
  public checkBadWord = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        console.log(req.body);
        if (!req
            || !req.body
            || !req.body.hasOwnProperty('sentence')) {
          return resolvePr({
            data: [],
            messageErr: 'Call error param not valid',
            status: 'FAILED',
          });
        }

        const { sentence } = req.body;

        const data = JSON.stringify({ sentence });

        const configCall = {
          data,
          method: 'post',
          maxBodyLength: Infinity,
          url: `${badWordUrl}/check`,
          headers: {
            'Content-Type': 'application/json',
          },
        };

        // @ts-ignore
        await axios.request(configCall)
          .then((response) => {
            logger.info(JSON.stringify(response.data));
            console.log({ response: response.data })

            const { status, message, data } = response.data;

            return resolvePr({ status, data, messageErr: message });
          })
          .catch((error) => {
            logger.info(error);
            return resolvePr({ data: [], messageErr: JSON.stringify(error) });
          });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }

  public addBadWord = async (req: IRequest): Promise<any> => {
    try {
      return await new Promise(async (resolvePr) => {
        console.log(req.body);
        if (!req
            || !req.body
            || !req.body.hasOwnProperty('words')
            || !req.body.hasOwnProperty('isPhrase')
            || !req.body.hasOwnProperty('lang')
        ) {
          return resolvePr({
            data: [],
            messageErr: 'Call error param not valid',
            status: 'FAILED',
          });
        }

        const { words, isPhrase, lang } = req.body;

        const data = JSON.stringify({ words, isPhrase, lang });

        const configCall = {
          data,
          method: 'post',
          maxBodyLength: Infinity,
          url: `${badWordUrl}/add`,
          headers: {
            'Content-Type': 'application/json',
          },
        };

        // @ts-ignore
        await axios.request(configCall)
            .then((response) => {
              logger.info(JSON.stringify(response.data));
              console.log({ response: response.data })

              const { status, message, data } = response.data;

              return resolvePr({ status, data, messageErr: message });
            })
            .catch((error) => {
              logger.info(error);
              return resolvePr({ data: [], messageErr: JSON.stringify(error) });
            });
      })
    } catch (err) {
      logger.info(err);
      return { codeALT: '209', messageErr: JSON.stringify(err) };
    }
  }
}

const insBadWordService = new BadWordFilterService();
export default insBadWordService;
