import { Router } from 'express';
import {
    // CRUD controllers
    createPrompting,
    updatePrompting,
    deletePrompting,
    getPromptings,
    getPromptingById,
    
    // Specialized controllers
    getPromptingsByLanguage,
    getPromptingsByCodeType,
    getPromptingsByStatus,
    
    // Bulk operations
    bulkDeletePromptings,
    bulkUpdatePromptings
} from '../controllers/promptings.controller';

import {
    // Validation middleware
    validateCreatePrompting,
    validateUpdatePrompting,
    validatePagination,
    validatePathParams,
    validateBulkOperation,
    validateFilterParams,
    validateSearchParams
} from '../middleware/promptings.validation';

const router = Router();

// ============ Basic CRUD Routes ============

/**
 * @route POST /api/promptings
 * @desc Create new prompting
 * @body {PromptingsModel} - Prompting data
 */
router.post('/', validateCreatePrompting, createPrompting);

/**
 * @route PUT /api/promptings/:id
 * @desc Update prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 * @body {Partial<PromptingsModel>} - Update data
 */
router.put('/:id', validateUpdatePrompting, updatePrompting);

/**
 * @route DELETE /api/promptings/:id
 * @desc Delete prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 */
router.delete('/:id', validatePathParams, deletePrompting);

/**
 * @route GET /api/promptings
 * @desc Get all promptings with pagination and filtering
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 * @query {string} LNG_TP - Filter by language type
 * @query {string} COL_CD - Filter by column code
 * @query {string} COL_CD_TP - Filter by column code type
 * @query {string} COL_CD_TP_NM - Filter by column code type name
 * @query {number} ACTIVE_YN - Filter by active status (0 | 1)
 */
router.get('/', validatePagination, validateFilterParams, getPromptings);

/**
 * @route GET /api/promptings/:id
 * @desc Get prompting by ID
 * @params {string} id - Prompting ID (ObjectId)
 */
router.get('/:id', validatePathParams, getPromptingById);

// ============ Specialized Query Routes ============

/**
 * @route GET /api/promptings/language/:lng_tp
 * @desc Get promptings by language type
 * @params {string} lng_tp - Language type
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 */
router.get('/language/:lng_tp', validatePathParams, validatePagination, getPromptingsByLanguage);

/**
 * @route GET /api/promptings/code-type/:col_cd_tp
 * @desc Get promptings by column code type
 * @params {string} col_cd_tp - Column code type
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 */
router.get('/code-type/:col_cd_tp', validatePathParams, validatePagination, getPromptingsByCodeType);

/**
 * @route GET /api/promptings/status/:status
 * @desc Get promptings by status (active/inactive)
 * @params {string} status - Status ('active' | 'inactive')
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 */
router.get('/status/:status', validatePathParams, validatePagination, getPromptingsByStatus);

// ============ Bulk Operations Routes ============

/**
 * @route DELETE /api/promptings/bulk
 * @desc Bulk delete promptings
 * @body {string[]} ids - Array of prompting IDs to delete
 */
router.delete('/bulk', (req, res, next) => {
    req.params.operation = 'delete';
    next();
}, validateBulkOperation, bulkDeletePromptings);

/**
 * @route PUT /api/promptings/bulk
 * @desc Bulk update promptings
 * @body {string[]} ids - Array of prompting IDs to update
 * @body {Partial<PromptingsModel>} updateData - Data to update
 */
router.put('/bulk', (req, res, next) => {
    req.params.operation = 'update';
    next();
}, validateBulkOperation, bulkUpdatePromptings);

// ============ Search Routes ============

/**
 * @route GET /api/promptings/search
 * @desc Search promptings with advanced filters
 * @query {string} search - Search term
 * @query {string} searchField - Field to search in (LNG_TP, COL_CD, COL_CD_TP, COL_CD_TP_NM)
 * @query {number} page - Page number (default: 1)
 * @query {number} limit - Items per page (default: 10, max: 100)
 * @query {string} sort - Sort field (default: '_id')
 * @query {string} sortOrder - Sort order ('asc' | 'desc', default: 'desc')
 */
router.get('/search', validateSearchParams, validatePagination, (req, res) => {
    // Build search filter
    const { search, searchField } = req.query;
    let filter: any = {};
    
    if (search && searchField) {
        filter[searchField as string] = { $regex: search, $options: 'i' };
    } else if (search) {
        // Search across multiple fields if no specific field is specified
        filter = {
            $or: [
                { LNG_TP: { $regex: search, $options: 'i' } },
                { COL_CD: { $regex: search, $options: 'i' } },
                { COL_CD_TP: { $regex: search, $options: 'i' } },
                { COL_CD_TP_NM: { $regex: search, $options: 'i' } }
            ]
        };
    }
    
    // Add filter to request and call getPromptings
    req.query = { ...req.query, ...filter };
    getPromptings(req, res);
});

// ============ Statistics Routes ============

/**
 * @route GET /api/promptings/stats/overview
 * @desc Get promptings statistics overview
 */
router.get('/stats/overview', async (req, res) => {
    try {
        // This is a placeholder for statistics functionality
        // You can implement actual statistics logic here
        res.status(200).json({
            success: true,
            message: 'Promptings statistics retrieved successfully',
            data: {
                note: 'Statistics functionality can be implemented here',
                endpoints: [
                    'Total count by language',
                    'Total count by code type',
                    'Active vs inactive count',
                    'Most used language types',
                    'Most used code types'
                ]
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Internal server error',
            error: error
        });
    }
});

// ============ Health Check Route ============

/**
 * @route GET /api/promptings/health
 * @desc Promptings API health check
 */
router.get('/health', (req, res) => {
    res.status(200).json({
        success: true,
        message: 'Promptings API is healthy',
        data: {
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            collection: 'ssv_dic'
        }
    });
});

export default router;
