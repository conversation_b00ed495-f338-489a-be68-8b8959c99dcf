import express from 'express';
import uploadController from '../../controllers/upload.controller';
import uploadSchema from '../../constants/schema/upload.schema';
import { celebrate } from 'celebrate';

const router = express.Router();

router.post(
  '/',
  celebrate(uploadSchema.upload),
  uploadController.upload,
);

router.post(
  '/delete',
  celebrate(uploadSchema.delete),
  uploadController.deleteFile,
);

export default router;
