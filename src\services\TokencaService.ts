
const fs = require('fs')
const path = require('path');
const dir_upload_order =  process.env.DIR_UPLOAD_ORDER || 'uploads-order-xml'

class TokenCAService {

    public uploadToFTP(filename:string, base64: string){
        // kiem tra folder exist
        // directory to check if exists
        // check if directory exists
        if(!filename || !base64){
            return {
                success: false,
                message:"Filename and base64 no empty"
            }
        }
        try {
            fs.access(dir_upload_order, (error:any) => {
                // To check if the given directory 
                // already exists or not
                if (error) {
                  // If current directory does not exist
                  // then create it
                    const resMkdir = fs.mkdir(dir_upload_order, (error:any) => {
                        if (error) {
                            return {
                                message: "Create folder error",
                                success: false
                            }
                        } else {
                            const result = this.base64ToJson(base64, filename)
                            return {
                                message: "",
                                success: result
                            }
                        }
                    });
                    console.log('resMkdir: ', resMkdir);

                } else {
                    const result = this.base64ToJson(base64, filename)
                    return {
                        message: "",
                        success: result
                    }
                }
            });
        } catch (error) {
            console.log('uploadToFTP error: ', error);
            return {
                message: "uploadToFTP error",
                success: false
            }
        }
    }

    private base64ToJson(base64String: string, filename:string) {
        try {
            const fileDataDecoded = Buffer.from(base64String,'base64');
            fs.writeFile(dir_upload_order + "/" + filename, fileDataDecoded,function(err:any){
                if(err) return false;
                return true
            })
            return true
        } catch (error) {
            console.log('base64ToJson error: ', error);
            return false            
        }
    }

}
const insTokenService = new TokenCAService()
export default insTokenService