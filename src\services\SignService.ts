import logger from '../config/logger';
import glb_sv from '../share/global_service';

const fs = require('fs');
const dir_upload_sign = process.env.DIR_UPLOAD_SIGN || 'sign';

class SignService {

    public uploadToServer(filename:string, base64: string) : Promise<any> {

        return new Promise(async (resolve) => {
            // kiem tra folder exist
            // directory to check if exists
            // check if directory exists
            if(!filename || !base64){
                return resolve( {
                    success: false,
                    message:"Filename and base64 no empty"
                });
            }
            // const checkSize = await this.base64Size(base64);

            // if(!checkSize) {
            //     logger.info("check Size >>>" +checkSize);
            //     return resolve(  {
            //         message: "File > 1 MB",
            //         success: false
            //     });
            // }

            try {
                fs.access(dir_upload_sign, async (error: any) => {
                    // To check if the given directory
                    // already exists or not
                    if (error) {
                        logger.info('dir >> ' + dir_upload_sign + '>>> current directory does not exist need create !');
                        // If current directory does not exist
                        // then create it
                        const resMkdir = fs.mkdir(dir_upload_sign, async (error: any) => {
                            if (error) {
                                logger.info('dir >> ' + dir_upload_sign + '>>> create error!');
                                return resolve(  {
                                    message: "Create folder error",
                                    success: false
                                });
                            } else {
                                logger.info('dir >> ' + dir_upload_sign + '>>> create ok!');
                                const result = await this.base64ToFile(base64, filename);
                                return resolve(  {
                                    message: filename +" upload ok.",
                                    success: result
                                });
                            }
                        });


                    } else {
                        const result = await this.base64ToFile(base64, filename);
                        return resolve(  {
                            message: filename +" upload ok.",
                            success: result
                        });
                    }
                });
            } catch (error) {
                console.log('upload error: ', error);
                return resolve(  {
                    message: "upload error",
                    success: false
                })
            }
        }).catch(error =>{
            logger.info(error);
            return(  {
                message: "Error Server",
                success: false
            });
        });


    }

    private base64Size = async (base64String: string):Promise<boolean> => {
        const stringLength = base64String.length
        const sizeInBytes = stringLength * (3 / 4) - 2;
        const sizeInKb = sizeInBytes  / 1000;
        const sizeInMb = sizeInKb  / 1000;

        const maxSizeKb = 1024;

        logger.info('sizeInKb  >> ' + sizeInKb + '');
        logger.info('sizeInMb  >> ' + sizeInMb + '');
        logger.info('Compare  >> ' + (sizeInKb - maxSizeKb) + '');
        // resize 
        if ((sizeInKb - maxSizeKb) >0)
            return false;

        return true;
    }

    private base64ToFile = async (base64String: string, filename: string): Promise<boolean> => {
        try {
            logger.info('filename >> ' + filename + ' to file begin');
            const fileDataDecoded = Buffer.from(base64String, 'base64');
            let flag = false;

            await new Promise((resolve, reject) => {
                fs.writeFile(dir_upload_sign + '/' + filename, fileDataDecoded, async function(err: any) {
                    if (!err) {
                        flag = true;
                        await glb_sv.resizeImage(dir_upload_sign + '/' + filename, 1000);
                    } else {
                        logger.info('filename >> ' + filename + ' >>> error ' + err);
                    }
                    resolve(flag);
                });
            });

            logger.info('filename >> ' + filename + ' >>> save success ' + flag + ' >> to file end');
            return flag;

        } catch (error) {
            logger.info('base64ToImg error: ' + error);
            return false;
        }
    };

}
const insSignService = new SignService()
export default insSignService
