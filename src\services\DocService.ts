import logger from '../config/logger';
const fs = require('fs');
const _ = require("lodash");

const pathDocEN =   process.env.URL_AGREE_EN;
const pathDocVI =   process.env.URL_AGREE_VI;
logger.info("pathDocEN >>" + pathDocEN);
logger.info("URL_AGREE_VI >>" + pathDocVI);


class DocService {
    public returnUrlDoc(language:string) : Promise<any> {
        return new Promise(async (resolve) => {
            let url ="";
            if(language == 'VI') {
                url = pathDocVI;
            }

            if(language == 'EN') {
                url = pathDocEN;
            }

            return resolve(url);
        })
    }
}

const insDocService = new DocService();
export default insDocService;

